<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- WhatsApp Connection Status View for Normal Users -->
        <record id="view_whatsapp_config_normal_user_form" model="ir.ui.view">
            <field name="name">whatsapp.config.normal.user.form</field>
            <field name="model">whatsapp.config</field>
            <field name="arch" type="xml">
                <form string="My WhatsApp Connection" create="false" edit="false" delete="false">
                    <header>
                        <!-- Reconnection Buttons -->
                        <button name="action_setup_instance" type="object" string="Connect WhatsApp"
                                class="btn-primary" icon="fa-plus"
                                invisible="instance_status == 'connected'"
                                groups="whatsapp_evolution.group_whatsapp_normal_user"/>

                        <button name="action_reconnect_instance" type="object" string="Reconnect WhatsApp"
                                class="btn-warning" icon="fa-refresh"
                                invisible="instance_status == 'connected' or not instance_name"
                                groups="whatsapp_evolution.group_whatsapp_normal_user"/>

                        <button name="action_refresh_status" type="object" string="Refresh Status"
                                class="btn-secondary" icon="fa-sync"
                                invisible="not instance_name"
                                groups="whatsapp_evolution.group_whatsapp_normal_user"/>

                        <!-- Status Badges -->
                        <field name="instance_status" widget="badge"
                               decoration-success="instance_status == 'connected'"
                               decoration-warning="instance_status in ['creating', 'waiting_qr', 'connecting']"
                               decoration-danger="instance_status == 'error'"
                               decoration-muted="instance_status == 'disconnected'"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <i class="fa fa-whatsapp text-success"/> WhatsApp Connection Status
                            </h1>
                        </div>
                        
                        <!-- Connection Status Panel -->
                        <div class="alert alert-success" role="alert" invisible="instance_status != 'connected'">
                            <h4>
                                <i class="fa fa-check-circle"/>
                                WhatsApp Connected
                            </h4>
                            <p>Your WhatsApp is connected and ready to send messages.</p>
                            <p><strong>Last checked:</strong> <field name="last_connection_check" readonly="1" nolabel="1"/></p>
                        </div>

                        <div class="alert alert-warning" role="alert" invisible="instance_status not in ['creating', 'waiting_qr', 'connecting']">
                            <h4>
                                <i class="fa fa-clock-o"/>
                                WhatsApp Connecting
                            </h4>
                            <p>Your WhatsApp is in the process of connecting. Please wait or scan the QR code if shown below.</p>
                        </div>

                        <div class="alert alert-danger" role="alert" invisible="instance_status not in ['disconnected', 'error']">
                            <h4>
                                <i class="fa fa-exclamation-triangle"/>
                                WhatsApp Disconnected
                            </h4>
                            <p>Your WhatsApp connection has been lost. Click the "Reconnect WhatsApp" button above to restore the connection.</p>
                            <p invisible="not last_error_message">
                                <strong>Error:</strong> <field name="last_error_message" readonly="1" nolabel="1"/>
                            </p>
                        </div>

                        <div class="alert alert-info" role="alert" invisible="instance_name">
                            <h4>
                                <i class="fa fa-info-circle"/>
                                WhatsApp Not Set Up
                            </h4>
                            <p>WhatsApp has not been set up yet. Click "Connect WhatsApp" above to get started.</p>
                        </div>

                        <!-- QR Code Display (if available) -->
                        <group string="Scan QR Code" invisible="not qr_code_base64">
                            <div class="text-center">
                                <h5>Scan this QR code with your WhatsApp mobile app:</h5>
                                <field name="qr_code_base64" widget="image"
                                       options="{'size': [300, 300]}"
                                       style="border: 1px solid #ddd; padding: 10px;"/>
                                <br/>
                                <p class="text-muted mt-2" invisible="not pairing_code">
                                    Alternative pairing code: <strong><field name="pairing_code" readonly="1" nolabel="1"/></strong>
                                </p>
                                <p class="text-info mt-3">
                                    <i class="fa fa-mobile"/> 
                                    Open WhatsApp on your phone → Settings → Linked Devices → Link a Device → Scan this QR code
                                </p>
                            </div>
                        </group>

                        <!-- Hidden fields needed for functionality -->
                        <group invisible="1">
                            <field name="name"/>
                            <field name="instance_name"/>
                            <field name="qr_code_data"/>
                            <field name="last_error_message"/>
                            <field name="company_id"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- WhatsApp Connection List View for Normal Users -->
        <record id="view_whatsapp_config_normal_user_tree" model="ir.ui.view">
            <field name="name">whatsapp.config.normal.user.list</field>
            <field name="model">whatsapp.config</field>
            <field name="arch" type="xml">
                <list string="My WhatsApp Connections" create="false" edit="false" delete="false">
                    <field name="name"/>
                    <field name="instance_status" widget="badge"
                           decoration-success="instance_status == 'connected'"
                           decoration-warning="instance_status in ['creating', 'waiting_qr', 'connecting']"
                           decoration-danger="instance_status == 'error'"
                           decoration-muted="instance_status == 'disconnected'"/>
                    <field name="last_connection_check"/>
                    <button name="action_reconnect_instance" type="object" string="Reconnect"
                            class="btn-warning btn-sm" icon="fa-refresh"
                            invisible="instance_status == 'connected'"
                            groups="whatsapp_evolution.group_whatsapp_normal_user"/>
                </list>
            </field>
        </record>

        <!-- WhatsApp Normal User Action -->
        <record id="action_whatsapp_normal_user" model="ir.actions.act_window">
            <field name="name">My WhatsApp Connection</field>
            <field name="res_model">whatsapp.config</field>
            <field name="view_mode">list,form</field>
            <field name="view_ids" eval="[(5, 0, 0),
                                          (0, 0, {'view_mode': 'list', 'view_id': ref('view_whatsapp_config_normal_user_tree')}),
                                          (0, 0, {'view_mode': 'form', 'view_id': ref('view_whatsapp_config_normal_user_form')})]"/>
            <field name="domain">[]</field>
            <field name="context">{}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Your WhatsApp connection will appear here
                </p>
                <p>
                    This is where you can check your WhatsApp connection status and reconnect if needed.
                </p>
            </field>
        </record>

    </data>
</odoo>
