<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Send WhatsApp Message Wizard Form View -->
        <record id="view_whatsapp_send_message_wizard_form" model="ir.ui.view">
            <field name="name">whatsapp.send.message.wizard.form</field>
            <field name="model">whatsapp.send.message.wizard</field>
            <field name="arch" type="xml">
                <form string="Send WhatsApp Message">
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <span invisible="not is_test">Send Test WhatsApp Message</span>
                                <span invisible="is_test">Send WhatsApp Message</span>
                            </h1>
                        </div>
                        
                        <group>
                            <group name="config_group">
                                <field name="config_id" options="{'no_create': True, 'no_edit': True}"/>
                                <field name="is_test" invisible="1"/>
                            </group>
                            <group name="timing_group">
                                <field name="delay" widget="integer"/>
                            </group>
                        </group>
                        
                        <group string="Message Details">
                            <field name="phone_number" placeholder="+1234567890"/>
                            <field name="message_text" widget="text" placeholder="Enter your WhatsApp message here..."/>
                        </group>
                        
                        <div class="alert alert-info" role="alert" invisible="not is_test">
                            <strong>Test Message:</strong> This will send a test message to verify your WhatsApp configuration is working correctly.
                        </div>
                        
                        <div class="alert alert-warning" role="alert">
                            <strong>Important:</strong> Make sure the phone number includes the country code (e.g., +1 for US, +44 for UK).
                        </div>
                    </sheet>
                    
                    <footer>
                        <button name="action_send_and_close" type="object" string="Send Message" class="btn-primary"/>
                        <button name="action_send_message" type="object" string="Send &amp; Keep Open" class="btn-secondary"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <!-- Send WhatsApp Message Wizard Action -->
        <record id="action_whatsapp_send_message_wizard" model="ir.actions.act_window">
            <field name="name">Send WhatsApp Message</field>
            <field name="res_model">whatsapp.send.message.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="view_id" ref="view_whatsapp_send_message_wizard_form"/>
        </record>

    </data>
</odoo>
