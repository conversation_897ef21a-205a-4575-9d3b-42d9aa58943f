# Arabic Translation Guide for WhatsApp Evolution Module

## Overview
This document provides a comprehensive guide for the Arabic translation implementation in the WhatsApp Evolution module. The module is now fully translated to Arabic with complete coverage of all models, views, wizards, and messages.

## Translation Coverage

### ✅ **Models Translated**
1. **WhatsApp Config** (`whatsapp.config`)
   - All field labels and help texts
   - All status messages and error messages
   - All method messages and notifications

2. **WhatsApp Settings** (`whatsapp.settings`)
   - Default settings configuration
   - All field descriptions

3. **WhatsApp Instance Wizard** (`whatsapp.instance.wizard`)
   - Setup wizard interface
   - Status messages and instructions

4. **WhatsApp Send Message Wizard** (`whatsapp.send.message.wizard`)
   - Message sending interface
   - Validation messages

5. **WhatsApp Message Log** (`whatsapp.message`)
   - Message history and logging
   - Status tracking

6. **WhatsApp Business Template** (`whatsapp.business.template`)
   - Template management
   - Template types and descriptions

7. **WhatsApp Stock Monitor** (`whatsapp.stock.monitor`)
   - Stock monitoring configuration
   - Alert settings

8. **WhatsApp Payment Monitor** (`whatsapp.payment.monitor`)
   - Payment monitoring setup
   - Reminder configurations

### ✅ **Views Translated**
1. **Configuration Views**
   - Main configuration form
   - Configuration list view
   - Search and filter options

2. **Wizard Views**
   - Instance setup wizard
   - Message sending wizard
   - All buttons and labels

3. **Normal User Views**
   - Simplified reconnection interface
   - Connection status display
   - QR code instructions

4. **Business Template Views**
   - Template creation and editing
   - Template type selection

5. **Monitoring Views**
   - Stock monitoring setup
   - Payment monitoring configuration

### ✅ **Menu Items Translated**
- Main WhatsApp Evolution menu
- Configuration submenu
- Messaging submenu
- Automation submenu
- Normal user menu ("My WhatsApp")

### ✅ **Security Groups Translated**
- WhatsApp Manager
- WhatsApp User
- WhatsApp Normal User

### ✅ **Messages and Notifications**
- Success messages
- Error messages
- Status updates
- Validation messages
- Help instructions

## How to Enable Arabic Translation

### 1. **Install Arabic Language**
```bash
# In Odoo, go to:
Settings > Translations > Languages > Install Language
# Select Arabic and install
```

### 2. **Update Translation Files**
```bash
# Update the module to load translations
python odoo-bin -d your_database -u whatsapp_evolution --stop-after-init
```

### 3. **Switch User Language**
```bash
# For each user:
Settings > Users & Companies > Users
# Select user > Preferences tab > Language: Arabic
```

### 4. **Load Translations**
```bash
# In Odoo interface:
Settings > Translations > Import/Export > Import Translation
# Select Arabic language and the module
```

## Translation File Structure

### **File Location**
```
custom_addons/whatsapp_evolution/i18n/ar.po
```

### **Translation Format**
```po
#. module: whatsapp_evolution
#: model:ir.model.fields,field_description:whatsapp_evolution.field_whatsapp_config__name
msgid "Configuration Name"
msgstr "اسم التكوين"
```

## Key Arabic Translations

### **Common Terms**
| English | Arabic |
|---------|--------|
| WhatsApp Evolution | واتساب إيفوليوشن |
| Configuration | التكوين |
| Instance | المثيل |
| Connection | الاتصال |
| Message | الرسالة |
| Status | الحالة |
| Active | نشط |
| Connected | متصل |
| Disconnected | منقطع |
| Send Message | إرسال رسالة |
| QR Code | رمز الاستجابة السريعة |
| Pairing Code | رمز الإقران |
| Refresh Status | تحديث الحالة |
| Test Connection | اختبار الاتصال |

### **Status Messages**
| English | Arabic |
|---------|--------|
| Connected | متصل |
| Disconnected | منقطع |
| Connecting | جاري الاتصال |
| Creating Instance | إنشاء المثيل |
| Waiting for QR Scan | في انتظار مسح رمز الاستجابة السريعة |
| Error | خطأ |

### **User Interface Elements**
| English | Arabic |
|---------|--------|
| My WhatsApp | واتساب الخاص بي |
| WhatsApp Connection | اتصال واتساب |
| Reconnect WhatsApp | إعادة الاتصال بواتساب |
| Setup Instance | إعداد المثيل |
| Business Templates | قوالب الأعمال |
| Stock Monitoring | مراقبة المخزون |
| Payment Monitoring | مراقبة المدفوعات |

## Testing Arabic Translation

### **1. Verify Field Labels**
- Check all form views display Arabic labels
- Verify help texts are in Arabic
- Confirm button labels are translated

### **2. Test Messages**
- Send test messages and verify Arabic notifications
- Check error messages display in Arabic
- Verify success messages are translated

### **3. Check Menu Items**
- Ensure all menu items show in Arabic
- Verify submenu translations
- Check action names are translated

### **4. Validate User Roles**
- Test Normal User interface in Arabic
- Verify Manager interface translations
- Check User role translations

## Maintenance and Updates

### **Adding New Translations**
1. Add new msgid/msgstr pairs to `ar.po`
2. Update the module: `python odoo-bin -d db -u whatsapp_evolution`
3. Test the new translations

### **Updating Existing Translations**
1. Modify the msgstr in `ar.po`
2. Update the module
3. Clear browser cache if needed

### **Translation Best Practices**
- Keep translations contextually appropriate
- Use formal Arabic for business terms
- Maintain consistency across similar terms
- Test translations with Arabic-speaking users

## Troubleshooting

### **Translation Not Showing**
1. Check user language setting
2. Verify module is updated
3. Clear browser cache
4. Check translation file syntax

### **Partial Translation**
1. Verify all msgid entries have msgstr
2. Check for syntax errors in .po file
3. Update module after changes

### **RTL Layout Issues**
- Arabic is RTL (Right-to-Left)
- Odoo handles RTL automatically
- Custom CSS may need RTL adjustments

## Support

For translation issues or improvements:
1. Check the translation file for missing entries
2. Verify module updates are applied
3. Test with different user roles
4. Contact module maintainer for complex issues

---

**Note**: This translation covers all current functionality. When adding new features, ensure corresponding Arabic translations are added to maintain complete coverage.
