<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">

        <!-- Default Low Stock Alert Template -->
        <record id="default_low_stock_alert" model="whatsapp.business.template">
            <field name="template_code">low_stock_alert</field>
            <field name="template_name">Low Stock Alert</field>
            <field name="template_category">inventory</field>
            <field name="trigger_type">threshold</field>
            <field name="schedule_frequency">realtime</field>
            <field name="default_message">تنبيه نقص المخزون

المنتج: {product_name}
الموقع: {location_name}
المخزون الحالي: {current_qty} {uom}
التوقعات: {forecast_qty} {uom}
الحد الأدنى: {min_qty} {uom}
المطلوب طلبه: {to_order} {uom}

يرجى إعادة التخزين فوراً</field>
            <field name="custom_message">تنبيه نقص المخزون

المنتج: {product_name}
الموقع: {location_name}
المخزون الحالي: {current_qty} {uom}
التوقعات: {forecast_qty} {uom}
الحد الأدنى: {min_qty} {uom}
المطلوب طلبه: {to_order} {uom}

يرجى إعادة التخزين فوراً</field>
            <field name="sequence" eval="10"/>
            <field name="active" eval="True"/>
            <field name="company_id" ref="base.main_company"/>
        </record>

        <!-- Customer Payment Confirmation Template -->
        <record id="customer_payment_confirmation" model="whatsapp.business.template">
            <field name="template_code">customer_payment_confirmation</field>
            <field name="template_name">Customer Payment Confirmation</field>
            <field name="template_category">financial</field>
            <field name="trigger_type">event</field>
            <field name="schedule_frequency">realtime</field>
            <field name="default_message">شكراً لك على الدفع

عزيزي {partner_name}

تم تأكيد الدفعة بنجاح
المبلغ: {amount} {currency}
التاريخ: {payment_date}
طريقة الدفع: {payment_method}
المرجع: {payment_reference}

رصيد الحساب:
{balance_info}

نشكرك على ثقتك بنا

سيتم إرسال الإيصال منفصلاً</field>
            <field name="custom_message">شكراً لك على الدفع

عزيزي {partner_name}

تم تأكيد الدفعة بنجاح
المبلغ: {amount} {currency}
التاريخ: {payment_date}
طريقة الدفع: {payment_method}
المرجع: {payment_reference}

رصيد الحساب:
{balance_info}

نشكرك على ثقتك بنا

سيتم إرسال الإيصال منفصلاً</field>
            <field name="sequence" eval="20"/>
            <field name="active" eval="True"/>
            <field name="company_id" ref="base.main_company"/>
        </record>

        <!-- Vendor Payment Confirmation Template -->
        <record id="vendor_payment_confirmation" model="whatsapp.business.template">
            <field name="template_code">vendor_payment_confirmation</field>
            <field name="template_name">Vendor Payment Confirmation</field>
            <field name="template_category">financial</field>
            <field name="trigger_type">event</field>
            <field name="schedule_frequency">realtime</field>
            <field name="default_message">إشعار دفع للمورد

عزيزي {partner_name}،

تم دفع المبلغ المستحق
المبلغ: {amount} {currency}
التاريخ: {payment_date}
طريقة الدفع: {payment_method}
المرجع: {payment_reference}

رصيد الحساب:
{balance_info}

شكراً لخدماتكم

سيتم إرسال الإيصال منفصلاً</field>
            <field name="custom_message">إشعار دفع للمورد

عزيزي {partner_name}،

تم دفع المبلغ المستحق
المبلغ: {amount} {currency}
التاريخ: {payment_date}
طريقة الدفع: {payment_method}
المرجع: {payment_reference}

رصيد الحساب:
{balance_info}

شكراً لخدماتكم

سيتم إرسال الإيصال منفصلاً</field>
            <field name="sequence" eval="30"/>
            <field name="active" eval="True"/>
            <field name="company_id" ref="base.main_company"/>
        </record>

    </data>
</odoo>
