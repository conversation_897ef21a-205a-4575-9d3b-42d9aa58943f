<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- <PERSON><PERSON> Job for Low Stock Monitoring -->
        <record id="cron_whatsapp_low_stock_check" model="ir.cron">
            <field name="name">WhatsApp: Check Low Stock Alerts</field>
            <field name="model_id" ref="stock.model_stock_quant"/>
            <field name="state">code</field>
            <field name="code">model.cron_check_low_stock()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">hours</field>
            <field name="active">True</field>
            <field name="user_id" ref="base.user_root"/>
        </record>

        <!-- Cron Job for WhatsApp Instance Connection Monitoring -->
        <record id="cron_whatsapp_connection_monitor" model="ir.cron">
            <field name="name">WhatsApp: Monitor Instance Connections</field>
            <field name="model_id" ref="model_whatsapp_config"/>
            <field name="state">code</field>
            <field name="code">model._monitor_all_connections()</field>
            <field name="interval_number">5</field>
            <field name="interval_type">minutes</field>
            <field name="active">True</field>
            <field name="user_id" ref="base.user_root"/>
        </record>

        <!-- Cron Job for Scheduled Template Messages -->
        <record id="cron_whatsapp_scheduled_messages" model="ir.cron">
            <field name="name">WhatsApp: Send Scheduled Messages</field>
            <field name="model_id" ref="model_whatsapp_business_template"/>
            <field name="state">code</field>
            <field name="code">
# Send scheduled messages - placeholder for future implementation
# This will be implemented when we add scheduled templates
pass
            </field>
            <field name="interval_number">1</field>
            <field name="interval_type">hours</field>
            <field name="active">False</field>
            <field name="user_id" ref="base.user_root"/>
        </record>

    </data>
</odoo>
