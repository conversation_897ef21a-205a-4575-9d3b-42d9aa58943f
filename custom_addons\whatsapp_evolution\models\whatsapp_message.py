# -*- coding: utf-8 -*-

import json
from odoo import models, fields, api, _


class WhatsAppMessage(models.Model):
    _name = 'whatsapp.message'
    _description = 'WhatsApp Message Log'
    _order = 'sent_date desc'
    _rec_name = 'display_name'

    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True
    )
    
    config_id = fields.Many2one(
        'whatsapp.config',
        string='WhatsApp Configuration',
        required=True,
        ondelete='cascade'
    )

    template_id = fields.Many2one(
        'whatsapp.business.template',
        string='Template',
        ondelete='set null',
        help='Template used to send this message'
    )
    
    phone_number = fields.Char(
        string='Phone Number',
        required=True
    )
    
    message_text = fields.Text(
        string='Message Text',
        required=True
    )
    
    status = fields.Selection([
        ('sent', 'Sent'),
        ('failed', 'Failed'),
        ('pending', 'Pending'),
    ], string='Status', default='pending', required=True)
    
    sent_date = fields.Datetime(
        string='Sent Date',
        default=fields.Datetime.now
    )
    
    response_data = fields.Text(
        string='API Response',
        help='Raw response from Evolution API'
    )
    
    error_message = fields.Text(
        string='Error Message',
        help='Error message if sending failed'
    )
    
    message_id = fields.Char(
        string='Message ID',
        help='Message ID from WhatsApp/Evolution API',
        compute='_compute_message_id',
        store=True
    )
    
    remote_jid = fields.Char(
        string='Remote JID',
        help='WhatsApp JID from response',
        compute='_compute_message_details',
        store=True
    )
    
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        related='config_id.company_id',
        store=True
    )

    @api.depends('phone_number', 'sent_date')
    def _compute_display_name(self):
        for record in self:
            if record.phone_number and record.sent_date:
                record.display_name = f"{record.phone_number} - {record.sent_date.strftime('%Y-%m-%d %H:%M')}"
            else:
                record.display_name = record.phone_number or 'New Message'

    @api.depends('response_data')
    def _compute_message_id(self):
        for record in self:
            if record.response_data:
                try:
                    data = json.loads(record.response_data)
                    record.message_id = data.get('key', {}).get('id', '')
                except:
                    record.message_id = ''
            else:
                record.message_id = ''

    @api.depends('response_data')
    def _compute_message_details(self):
        for record in self:
            if record.response_data:
                try:
                    data = json.loads(record.response_data)
                    record.remote_jid = data.get('key', {}).get('remoteJid', '')
                except:
                    record.remote_jid = ''
            else:
                record.remote_jid = ''

    def action_resend_message(self):
        """Resend a failed message"""
        self.ensure_one()
        
        if self.status == 'sent':
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Warning'),
                    'message': _('This message was already sent successfully.'),
                    'type': 'warning',
                }
            }
        
        try:
            response_data = self.config_id.send_message(
                self.phone_number,
                self.message_text
            )
            
            self.write({
                'status': 'sent',
                'response_data': json.dumps(response_data),
                'error_message': False,
                'sent_date': fields.Datetime.now(),
            })
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _('Message resent successfully!'),
                    'type': 'success',
                }
            }
            
        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Failed to resend message: %s') % str(e),
                    'type': 'danger',
                }
            }

    def action_view_response_data(self):
        """View the raw API response data"""
        self.ensure_one()
        
        if not self.response_data:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Data'),
                    'message': _('No response data available for this message.'),
                    'type': 'info',
                }
            }
        
        try:
            # Pretty format JSON
            data = json.loads(self.response_data)
            formatted_data = json.dumps(data, indent=2)
        except:
            formatted_data = self.response_data
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('API Response Data'),
            'res_model': 'whatsapp.response.viewer',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_message_id': self.id,
                'default_response_data': formatted_data,
            }
        }
