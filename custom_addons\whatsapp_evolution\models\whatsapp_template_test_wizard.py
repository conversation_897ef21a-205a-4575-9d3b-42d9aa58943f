# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class WhatsAppTemplateTestWizard(models.TransientModel):
    _name = 'whatsapp.template.test.wizard'
    _description = 'Test WhatsApp Business Template'

    template_id = fields.Many2one(
        'whatsapp.business.template',
        string='Template',
        required=True,
        readonly=True
    )
    
    phone_number = fields.Char(
        string='Test Phone Number',
        required=True,
        help='Phone number to send test message (with country code, e.g., +1234567890)'
    )
    
    test_variables = fields.Text(
        string='Test Variables',
        help='Variables to use for testing (JSON format or key=value pairs)'
    )
    
    preview_message = fields.Text(
        string='Message Preview',
        readonly=True,
        compute='_compute_preview_message'
    )

    @api.depends('template_id', 'test_variables')
    def _compute_preview_message(self):
        for wizard in self:
            if wizard.template_id:
                message = wizard.template_id.get_message_content()
                
                # Parse test variables
                if wizard.test_variables:
                    try:
                        # Try to parse as simple key=value format
                        variables = {}
                        for line in wizard.test_variables.split('\n'):
                            if '=' in line:
                                key, value = line.split('=', 1)
                                variables[key.strip()] = value.strip()
                        
                        # Replace variables in message
                        for var_name, var_value in variables.items():
                            placeholder = '{' + var_name + '}'
                            message = message.replace(placeholder, str(var_value))
                    except:
                        pass
                
                wizard.preview_message = message
            else:
                wizard.preview_message = ''

    def action_send_test_message(self):
        """Send test message"""
        self.ensure_one()
        
        if not self.phone_number:
            raise UserError(_('Please enter a phone number for testing.'))
        
        # Parse variables
        variables = {}
        if self.test_variables:
            try:
                for line in self.test_variables.split('\n'):
                    if '=' in line:
                        key, value = line.split('=', 1)
                        variables[key.strip()] = value.strip()
            except Exception as e:
                raise UserError(_('Invalid variable format. Use key=value format, one per line.'))
        
        # Send message
        try:
            self.template_id.send_template_message(
                variables=variables,
                recipients=[self.phone_number]
            )
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'type': 'success',
                    'message': _('Test message sent successfully to %s') % self.phone_number,
                    'sticky': False,
                }
            }
        except Exception as e:
            raise UserError(_('Failed to send test message: %s') % str(e))

    def action_send_and_close(self):
        """Send test message and close wizard"""
        result = self.action_send_test_message()
        return {'type': 'ir.actions.act_window_close'}
