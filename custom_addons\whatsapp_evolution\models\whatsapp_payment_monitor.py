# -*- coding: utf-8 -*-

import logging
import base64
import json
from odoo import models, fields, api
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class AccountPayment(models.Model):
    _inherit = 'account.payment'

    def write(self, vals):
        """Override write to trigger WhatsApp payment confirmation when payment is posted"""
        result = super().write(vals)

        # Debug logging
        if 'state' in vals:
            _logger.info(f"Payment {self.name if len(self) == 1 else 'multiple'} state changing to: {vals['state']}")

        # Check if payment state changed to paid (confirmed) - Odoo 18 uses 'paid' instead of 'posted'
        if 'state' in vals and vals['state'] == 'paid':
            for payment in self:
                try:
                    _logger.info(f"Triggering payment confirmation for payment: {payment.name}")
                    payment._send_payment_confirmation()
                except Exception as e:
                    _logger.error(f"Failed to send payment confirmation for payment {payment.name}: {e}")

        return result

    def _send_payment_confirmation(self):
        """Send WhatsApp payment confirmation with text message and PDF receipt"""
        self.ensure_one()

        _logger.info(f"🔔 Starting payment confirmation for: {self.name} | Partner: {self.partner_id.name}")

        # Get appropriate payment confirmation template based on partner type
        if self.partner_type == 'customer':
            template_code = 'customer_payment_confirmation'
        else:  # vendor
            template_code = 'vendor_payment_confirmation'

        template = self.env['whatsapp.business.template'].search([
            ('template_code', '=', template_code),
            ('active', '=', True),
            ('company_id', '=', self.company_id.id)
        ], limit=1)

        if not template:
            _logger.warning("❌ Payment confirmation template not found or not active")
            return

        _logger.info(f"✅ Found template: {template.template_name}")

        # Get recipient phone number from partner
        if not self.partner_id.mobile and not self.partner_id.phone:
            _logger.warning(f"❌ No phone number found for partner {self.partner_id.name}")
            return

        recipient_phone = self.partner_id.mobile or self.partner_id.phone
        _logger.info(f"📱 Recipient phone: {recipient_phone}")

        if not recipient_phone:
            return
        
        # Prepare payment data for message
        payment_data = self._get_payment_message_data()
        
        try:
            # Send text confirmation message
            template.send_template_message(
                variables=payment_data,
                recipients=[recipient_phone]
            )
            _logger.info(f"Payment confirmation sent for payment: {self.name}")
            
            # Send PDF receipt as separate message
            self._send_payment_receipt_pdf(recipient_phone, template)
            
        except Exception as e:
            _logger.error(f"Failed to send payment confirmation for {self.name}: {e}")

    def _get_payment_message_data(self):
        """Prepare payment data for WhatsApp message"""
        self.ensure_one()
        
        # Calculate balance information
        balance_info = self._get_balance_info()
        
        # Format payment method
        payment_method_display = self.payment_method_line_id.name if self.payment_method_line_id else 'Manual'
        
        return {
            'partner_name': self.partner_id.name,
            'amount': f"{self.amount:,.2f}",
            'currency': self.currency_id.symbol or self.currency_id.name,
            'payment_date': self.date.strftime('%Y-%m-%d'),
            'payment_method': payment_method_display,
            'payment_reference': self.payment_reference or self.name,
            'balance_info': balance_info,
            'company_name': self.company_id.name,
            'payment_type': 'received' if self.payment_type == 'inbound' else 'sent',
        }

    def _get_balance_info(self):
        """Get customer/vendor balance information"""
        self.ensure_one()

        _logger.info(f"🔍 Calculating balance for partner: {self.partner_id.name} (Type: {self.partner_type})")

        # Calculate balance using account move lines
        if self.partner_type == 'customer':
            # Customer balance (receivable) - what customer owes us
            domain = [
                ('partner_id', '=', self.partner_id.id),
                ('account_id.account_type', '=', 'asset_receivable'),
                ('move_id.state', '=', 'posted')
            ]
            move_lines = self.env['account.move.line'].search(domain)
            debit_sum = sum(move_lines.mapped('debit'))
            credit_sum = sum(move_lines.mapped('credit'))
            balance = debit_sum - credit_sum

            _logger.info(f"💰 Customer balance: Debit={debit_sum}, Credit={credit_sum}, Balance={balance}")

            if balance > 0.01:  # Small threshold to avoid rounding issues
                return f"مستحق عليه: {balance:,.2f} {self.currency_id.symbol}"
            else:
                return f"مستحق عليه: 0.00 {self.currency_id.symbol}"
        else:
            # Vendor balance (payable) - what we owe vendor
            domain = [
                ('partner_id', '=', self.partner_id.id),
                ('account_id.account_type', '=', 'liability_payable'),
                ('move_id.state', '=', 'posted')
            ]
            move_lines = self.env['account.move.line'].search(domain)
            debit_sum = sum(move_lines.mapped('debit'))
            credit_sum = sum(move_lines.mapped('credit'))
            balance = credit_sum - debit_sum

            _logger.info(f"💰 Vendor balance: Debit={debit_sum}, Credit={credit_sum}, Balance={balance}")

            if balance > 0.01:  # Small threshold to avoid rounding issues
                return f"مستحق له: {balance:,.2f} {self.currency_id.symbol}"
            else:
                return f"مستحق له: 0.00 {self.currency_id.symbol}"

    def _send_payment_receipt_pdf(self, recipient_phone, template):
        """Generate and send payment receipt PDF via WhatsApp"""
        self.ensure_one()

        _logger.info(f"📄 Starting PDF receipt generation for payment: {self.name}")

        try:
            # Generate PDF receipt using Odoo's default payment receipt report
            _logger.info("📋 Starting PDF generation...")

            try:
                # Search for any payment-related PDF report
                _logger.info("🔍 Searching for payment reports...")
                reports = self.env['ir.actions.report'].search([
                    ('model', '=', 'account.payment'),
                    ('report_type', '=', 'qweb-pdf')
                ])

                _logger.info(f"📋 Found {len(reports)} payment reports: {[r.name for r in reports]}")

                if not reports:
                    _logger.warning("⚠️ No payment PDF reports found, trying account.move reports...")
                    # Try account.move reports as fallback
                    reports = self.env['ir.actions.report'].search([
                        ('model', '=', 'account.move'),
                        ('report_type', '=', 'qweb-pdf')
                    ], limit=1)

                if not reports:
                    _logger.error("❌ No suitable PDF reports found")
                    return

                report = reports[0]
                _logger.info(f"✅ Using report: {report.name} (ID: {report.id})")

                # Generate PDF using the report's render method
                _logger.info(f"📄 Generating PDF with report ID: {report.id}")
                pdf_content, _ = report._render_qweb_pdf(report.id, [self.id])
                _logger.info(f"✅ PDF generated successfully, size: {len(pdf_content)} bytes")

            except Exception as e:
                _logger.error(f"❌ Error generating PDF: {e}")
                import traceback
                _logger.error(traceback.format_exc())
                return
            _logger.info(f"✅ PDF generated, size: {len(pdf_content)} bytes")

            # Encode PDF to base64
            pdf_base64 = base64.b64encode(pdf_content).decode('utf-8')
            _logger.info(f"✅ PDF encoded to base64, length: {len(pdf_base64)}")

            # Get WhatsApp configuration using template's method
            try:
                whatsapp_config = template.get_config()
                _logger.info(f"✅ Using WhatsApp config: {whatsapp_config.name}")
            except Exception as config_error:
                _logger.error(f"❌ No active WhatsApp configuration found: {config_error}")
                return
            
            # Prepare media message data with Arabic filename
            payment_ref = self.name.replace('/', '_')
            filename = f"إيصال_دفع_{payment_ref}.pdf"
            
            # Clean phone number (remove + for Evolution API)
            clean_phone = recipient_phone.replace('+', '').replace(' ', '').replace('-', '')

            media_data = {
                'number': clean_phone,
                'mediatype': 'document',
                'mimetype': 'application/pdf',
                'caption': f'📄 Payment Receipt - {self.name}',
                'media': pdf_base64,
                'fileName': filename
            }
            
            # Send PDF via WhatsApp Evolution API
            _logger.info(f"📤 Sending PDF via WhatsApp API to {recipient_phone}")
            response = whatsapp_config._send_media_message(media_data, template)
            _logger.info(f"📡 API Response: {response}")

            if response.get('status') == 'PENDING':
                _logger.info(f"✅ Payment receipt PDF sent successfully for payment: {self.name}")
            else:
                _logger.error(f"❌ Failed to send payment receipt PDF: {response}")

        except Exception as e:
            _logger.error(f"❌ Error sending payment receipt PDF for {self.name}: {e}")
            import traceback
            _logger.error(traceback.format_exc())


class WhatsappConfig(models.Model):
    _inherit = 'whatsapp.config'

    def _send_media_message(self, media_data, template=None):
        """Send media message via Evolution API"""
        self.ensure_one()

        import requests

        url = f"{self.server_url.rstrip('/')}/message/sendMedia/{self.instance_name}"
        headers = {
            'Content-Type': 'application/json',
            'apikey': self.api_key
        }

        _logger.info(f"📡 Sending media to URL: {url}")
        _logger.info(f"📋 Media data: {media_data}")

        try:
            response = requests.post(url, json=media_data, headers=headers, timeout=30)
            response.raise_for_status()

            response_data = response.json()

            # Log the media message (similar to text message logging)
            self.env['whatsapp.message'].create({
                'config_id': self.id,
                'template_id': template.id if template else False,
                'phone_number': media_data.get('number'),
                'message_text': f"Media: {media_data.get('fileName', 'Unknown file')}",
                'status': 'sent',
                'response_data': json.dumps(response_data),
                'sent_date': fields.Datetime.now(),
            })

            return response_data

        except requests.exceptions.RequestException as e:
            _logger.error(f"WhatsApp media API request failed: {e}")
            return {'status': 'ERROR', 'error': str(e)}
