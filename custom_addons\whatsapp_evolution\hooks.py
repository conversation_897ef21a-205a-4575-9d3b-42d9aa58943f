# -*- coding: utf-8 -*-

import logging
from odoo import api, SUPERUSER_ID

_logger = logging.getLogger(__name__)


def post_init_hook(cr, registry):
    """Create missing default business templates - NEVER overwrite existing ones"""
    _logger.info("Checking for missing WhatsApp business templates...")

    with api.Environment.manage():
        env = api.Environment(cr, SUPERUSER_ID, {})

        # Get the template model
        template_model = env['whatsapp.business.template']

        # Get main company
        main_company = env.ref('base.main_company', raise_if_not_found=False)
        if not main_company:
            main_company = env['res.company'].search([], limit=1)
        
        # Define default templates
        default_templates = [
            {
                'template_code': 'low_stock_alert',
                'template_name': 'Low Stock Alert',
                'template_category': 'inventory',
                'trigger_type': 'threshold',
                'schedule_frequency': 'realtime',
                'default_message': '''🚨 Low Stock Alert

📦 Product: {product_name}
📍 Location: {location_name}
📊 Current Stock: {current_qty} {uom}
📈 Forecast: {forecast_qty} {uom}
⚠️ Minimum: {min_qty} {uom}
📋 To Order: {to_order} {uom}

Please restock immediately!''',
                'sequence': 10,
                'active': True,
                'company_id': main_company.id,
            },
            {
                'template_code': 'payment_confirmation',
                'template_name': 'Payment Confirmation',
                'template_category': 'financial',
                'trigger_type': 'event',
                'schedule_frequency': 'realtime',
                'default_message': '''💰 Payment Received - Thank You!

Dear {partner_name},

✅ Payment Confirmed
💵 Amount: {amount} {currency}
📅 Date: {payment_date}
💳 Method: {payment_method}
📄 Reference: {payment_reference}

💼 Account Balance:
{balance_info}

Thank you for your payment!

📄 Receipt will be sent separately.''',
                'sequence': 20,
                'active': False,
                'company_id': main_company.id,
            },
        ]
        
        # DEVELOPMENT MODE: Always create templates (remove existence check)
        templates_created = 0

        for template_data in default_templates:
            template_code = template_data['template_code']

            # DEVELOPMENT: Always create template without checking if it exists
            template_data['custom_message'] = template_data['default_message']
            template_model.create(template_data)
            _logger.info(f"Created template: {template_code}")
            templates_created += 1

        _logger.info(f"Template processing completed: {templates_created} templates created")


def uninstall_hook(cr, registry):
    """Clean up when module is uninstalled"""
    _logger.info("Cleaning up WhatsApp business templates...")
    
    with api.Environment.manage():
        env = api.Environment(cr, SUPERUSER_ID, {})
        
        # Get all templates created by this module
        templates = env['whatsapp.business.template'].search([
            ('template_code', 'in', ['low_stock_alert', 'payment_confirmation'])
        ])
        
        if templates:
            templates.unlink()
            _logger.info(f"Removed {len(templates)} WhatsApp business templates")
