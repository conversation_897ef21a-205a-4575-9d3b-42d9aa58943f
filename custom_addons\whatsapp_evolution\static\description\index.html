<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>WhatsApp Evolution API Integration</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { text-align: center; margin-bottom: 30px; }
        .feature { margin: 20px 0; }
        .code { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 WhatsApp Evolution API Integration</h1>
        <p>Send WhatsApp messages directly from Odoo using Evolution API</p>
    </div>

    <div class="feature">
        <h3>✨ Features</h3>
        <ul>
            <li>Configure Evolution API server settings</li>
            <li>Send test WhatsApp messages</li>
            <li>Integration with Evolution API v2</li>
            <li>Support for multiple instances</li>
            <li>Message logging and status tracking</li>
            <li>Easy configuration interface</li>
        </ul>
    </div>

    <div class="feature">
        <h3>⚙️ Configuration</h3>
        <ol>
            <li>Go to <strong>WhatsApp > Configuration > WhatsApp Settings</strong></li>
            <li>Configure your Evolution API server URL, API key, and instance name</li>
            <li>Test the connection</li>
            <li>Start sending WhatsApp messages</li>
        </ol>
    </div>

    <div class="feature">
        <h3>📋 Requirements</h3>
        <ul>
            <li>Evolution API server running and accessible</li>
            <li>Valid API key and instance configured in Evolution API</li>
            <li>WhatsApp instance connected and active</li>
        </ul>
    </div>

    <div class="feature">
        <h3>📚 Documentation</h3>
        <p>For Evolution API documentation, visit: <a href="https://doc.evolution-api.com" target="_blank">https://doc.evolution-api.com</a></p>
    </div>
</body>
</html>
