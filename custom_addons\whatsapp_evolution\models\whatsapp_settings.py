# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class WhatsAppSettings(models.Model):
    _name = 'whatsapp.settings'
    _description = 'WhatsApp Evolution API Default Settings'
    _rec_name = 'name'

    name = fields.Char(
        string='Configuration Name',
        default='WhatsApp Default Settings',
        required=True
    )

    default_server_url = fields.Char(
        string='Default Evolution API Server URL',
        help='Default server URL that will be used for new WhatsApp configurations',
        default='https://whatsapp-n8n-evolution.ufzryk.easypanel.host',
        required=True
    )

    default_global_api_key = fields.Char(
        string='Default Global API Key',
        help='Default global API key that will be used for new WhatsApp configurations',
        default='429683C4C977415CAAFCCE10F7D57E12',
        required=True
    )

    active = fields.Boolean(
        string='Active',
        default=True
    )

    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.company,
        required=True
    )

    @api.model
    def get_default_settings(self):
        """Get the active default settings for the current company"""
        settings = self.search([
            ('active', '=', True),
            ('company_id', '=', self.env.company.id)
        ], limit=1)

        if not settings:
            # Create default settings if none exist
            settings = self.create({
                'name': 'WhatsApp Default Settings',
                'default_server_url': 'https://whatsapp-n8n-evolution.ufzryk.easypanel.host',
                'default_global_api_key': '429683C4C977415CAAFCCE10F7D57E12',
                'active': True,
                'company_id': self.env.company.id
            })

        return settings

    @api.model
    def get_default_server_url(self):
        """Get the default server URL from settings"""
        settings = self.get_default_settings()
        return settings.default_server_url

    @api.model
    def get_default_global_api_key(self):
        """Get the default global API key from settings"""
        settings = self.get_default_settings()
        return settings.default_global_api_key

    def action_save_settings(self):
        """Save the settings and show confirmation"""
        self.ensure_one()
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Settings Saved'),
                'message': _('WhatsApp default settings have been saved successfully.'),
                'type': 'success',
            }
        }
