# -*- coding: utf-8 -*-

import logging
from odoo import models, fields, api, _

_logger = logging.getLogger(__name__)


class StockQuant(models.Model):
    _inherit = 'stock.quant'

    @api.model
    def _check_low_stock_alerts(self):
        """Check for low stock and send WhatsApp alerts"""
        # Get active low stock alert template
        template = self.env['whatsapp.business.template'].search([
            ('template_code', '=', 'low_stock_alert'),
            ('active', '=', True),
            ('company_id', '=', self.env.company.id)
        ], limit=1)
        
        if not template:
            _logger.info("Low stock alert template not found or not active")
            return
        
        # Get products with low stock
        low_stock_products = self._get_low_stock_products(template)
        
        if not low_stock_products:
            _logger.info("No low stock products found")
            return
        
        # Send alerts for each low stock product
        for product_data in low_stock_products:
            try:
                template.send_template_message(
                    variables=product_data,
                    recipients=template.get_recipients_list()
                )
                _logger.info(f"Low stock alert sent for product: {product_data['product_name']}")
            except Exception as e:
                _logger.error(f"Failed to send low stock alert for {product_data['product_name']}: {e}")

    def _get_low_stock_products(self, template):
        """Get list of products with low stock using Odoo's reordering rules"""
        low_stock_products = []
        now = fields.Datetime.now()

        # Get all active reordering rules that need replenishment
        reorder_rules = self.env['stock.warehouse.orderpoint'].search([
            ('company_id', '=', self.env.company.id),
            ('active', '=', True),
            ('trigger', '=', 'auto')  # Only auto-triggered rules
        ])

        for rule in reorder_rules:
            # Skip if cooldown period hasn't passed
            if hasattr(rule, 'last_alert_sent') and rule.last_alert_sent:
                hours_since_alert = (now - rule.last_alert_sent).total_seconds() / 3600
                if hours_since_alert < template.alert_cooldown_hours:
                    continue

            # Use Odoo's built-in logic: qty_forecast < product_min_qty means need to reorder
            if rule.qty_forecast < rule.product_min_qty:
                # For critical stock only mode, check if completely out of stock
                if template.only_critical_stock and rule.qty_on_hand > 0:
                    continue

                product_data = {
                    'product_name': rule.product_id.display_name,
                    'current_qty': f"{rule.qty_on_hand:.2f}",
                    'forecast_qty': f"{rule.qty_forecast:.2f}",
                    'min_qty': f"{rule.product_min_qty:.2f}",
                    'max_qty': f"{rule.product_max_qty:.2f}",
                    'to_order': f"{rule.qty_to_order:.2f}",
                    'uom': rule.product_uom.name,
                    'location_name': rule.location_id.display_name,
                    'warehouse': rule.warehouse_id.name,
                    'product_code': rule.product_id.default_code or '',
                    'category': rule.product_id.categ_id.name,
                    'rule_id': rule.id,
                }
                low_stock_products.append(product_data)

                # Update last alert sent time
                if hasattr(rule, 'last_alert_sent'):
                    rule.last_alert_sent = now

        return low_stock_products

    def _is_below_threshold(self, current_qty, threshold_qty, template):
        """Check if current quantity is below threshold based on template settings"""
        if template.threshold_operator == 'less_than':
            return current_qty < threshold_qty
        elif template.threshold_operator == 'less_equal':
            return current_qty <= threshold_qty
        elif template.threshold_operator == 'equal':
            return current_qty == threshold_qty
        elif template.threshold_operator == 'greater_than':
            return current_qty > threshold_qty
        elif template.threshold_operator == 'greater_equal':
            return current_qty >= threshold_qty
        
        return current_qty <= threshold_qty  # Default behavior

    def _check_products_without_rules(self, low_stock_products, template):
        """Check products without reorder rules for very low stock"""
        # Get products that don't have reorder rules
        products_with_rules = self.env['stock.warehouse.orderpoint'].search([
            ('company_id', '=', self.env.company.id),
            ('active', '=', True)
        ]).mapped('product_id')
        
        # Get all stockable products
        all_products = self.env['product.product'].search([
            ('type', '=', 'product'),
            ('active', '=', True),
            ('id', 'not in', products_with_rules.ids)
        ])
        
        for product in all_products:
            # Get total stock across all locations
            quants = self.search([
                ('product_id', '=', product.id),
                ('quantity', '>', 0)
            ])
            
            current_qty = sum(quants.mapped('quantity'))
            
            # Alert if stock is very low (configurable threshold)
            threshold = template.threshold_value or 5.0  # Default 5 units
            
            if self._is_below_threshold(current_qty, threshold, template):
                # Check if we already added this product
                existing = any(p['product_name'] == product.display_name for p in low_stock_products)
                if not existing:
                    product_data = {
                        'product_name': product.display_name,
                        'current_qty': f"{current_qty:.2f}",
                        'min_qty': f"{threshold:.2f}",
                        'uom': product.uom_id.name,
                        'location_name': _('All Locations'),
                        'product_code': product.default_code or '',
                        'category': product.categ_id.name,
                    }
                    low_stock_products.append(product_data)

    @api.model
    def cron_check_low_stock(self):
        """Cron job to check low stock alerts"""
        try:
            self._check_low_stock_alerts()
        except Exception as e:
            _logger.error(f"Error in low stock cron job: {e}")


class ProductProduct(models.Model):
    _inherit = 'product.product'

    def write(self, vals):
        """Override write to trigger stock alerts when quantity changes"""
        result = super().write(vals)
        
        # If this is a stock quantity update, check for low stock
        if any(field in vals for field in ['qty_available', 'virtual_available']):
            # Check for low stock (without delay for now)
            try:
                self.env['stock.quant']._check_low_stock_alerts()
            except Exception as e:
                import logging
                _logger = logging.getLogger(__name__)
                _logger.error(f"Error checking low stock after product update: {e}")
        
        return result


class StockMove(models.Model):
    _inherit = 'stock.move'

    def _action_done(self, cancel_backorder=False):
        """Override to trigger stock alerts after stock moves"""
        result = super()._action_done(cancel_backorder=cancel_backorder)
        
        # Check for low stock after stock moves are completed
        try:
            self.env['stock.quant']._check_low_stock_alerts()
        except Exception as e:
            _logger.error(f"Error checking low stock after move: {e}")
        
        return result


class StockWarehouseOrderpoint(models.Model):
    _inherit = 'stock.warehouse.orderpoint'

    whatsapp_alert_enabled = fields.Boolean(
        string='WhatsApp Alert',
        default=True,
        help='Send WhatsApp alert when stock goes below minimum'
    )
    
    last_alert_sent = fields.Datetime(
        string='Last Alert Sent',
        readonly=True,
        help='When was the last WhatsApp alert sent for this product'
    )

    def write(self, vals):
        """Trigger stock check when reorder rules are updated"""
        result = super().write(vals)
        
        if any(field in vals for field in ['product_min_qty', 'product_max_qty', 'active']):
            # Check stock levels after updating reorder rules
            self.env['stock.quant']._check_low_stock_alerts()
        
        return result
