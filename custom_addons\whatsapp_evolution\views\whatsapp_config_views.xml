<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- WhatsApp Configuration Form View -->
        <record id="view_whatsapp_config_form" model="ir.ui.view">
            <field name="name">whatsapp.config.form</field>
            <field name="model">whatsapp.config</field>
            <field name="arch" type="xml">
                <form string="WhatsApp Configuration">
                    <header>
                        <!-- Instance Management Buttons -->
                        <button name="action_setup_instance" type="object" string="Setup Instance"
                                class="btn-primary" icon="fa-plus"
                                invisible="instance_status == 'connected'"/>

                        <button name="action_reconnect_instance" type="object" string="Reconnect"
                                class="btn-warning" icon="fa-refresh"
                                invisible="instance_status == 'connected' or not instance_name"/>

                        <button name="action_refresh_status" type="object" string="Refresh Status"
                                class="btn-secondary" icon="fa-sync"
                                invisible="not instance_name"/>

                        <button name="action_fetch_api_key" type="object" string="Fetch API Key"
                                class="btn-info" icon="fa-key"
                                invisible="not instance_name"
                                groups="whatsapp_evolution.group_whatsapp_user"
                                help="Fetch the instance-specific API key from Evolution API"/>

                        <button name="action_force_disconnect" type="object" string="Disconnect"
                                class="btn-danger" icon="fa-unlink"
                                invisible="instance_status != 'connected'"
                                groups="whatsapp_evolution.group_whatsapp_user"
                                confirm="Are you sure you want to disconnect this WhatsApp instance?"/>

                        <button name="test_connection" type="object" string="Test API Connection"
                                class="btn-secondary" icon="fa-plug"
                                groups="whatsapp_evolution.group_whatsapp_user"/>

                        <button name="action_send_test_message" type="object" string="Send Test Message"
                                class="btn-secondary" icon="fa-paper-plane"
                                invisible="instance_status != 'connected'"/>

                        <!-- Status Badges -->
                        <field name="instance_status" widget="badge"
                               decoration-success="instance_status == 'connected'"
                               decoration-warning="instance_status in ['creating', 'waiting_qr', 'connecting']"
                               decoration-danger="instance_status == 'error'"
                               decoration-muted="instance_status == 'disconnected'"/>

                        <field name="connection_status" widget="badge"
                               decoration-success="connection_status == 'connected'"
                               decoration-danger="connection_status == 'error'"
                               decoration-muted="connection_status == 'not_tested'"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_messages" type="object"
                                    class="oe_stat_button" icon="fa-comments">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_text">View</span>
                                    <span class="o_stat_text">Messages</span>
                                </div>
                            </button>

                            <button name="action_send_test_message" type="object"
                                    class="oe_stat_button" icon="fa-paper-plane"
                                    invisible="instance_status != 'connected'">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_text">Send Test</span>
                                    <span class="o_stat_text">Message</span>
                                </div>
                            </button>
                        </div>
                        
                        <widget name="web_ribbon" title="Default" bg_color="bg-success" 
                                invisible="not is_default"/>
                        
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only"/>
                            <h1><field name="name" placeholder="Configuration Name"/></h1>
                        </div>
                        
                        <group>
                            <group name="basic_info">
                                <field name="active"/>
                                <field name="is_default"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                            </group>
                            <group name="connection_info">
                                <field name="last_test_date" readonly="1"/>
                                <field name="connection_status" readonly="1"/>
                                <field name="last_connection_check" readonly="1"/>
                            </group>
                        </group>

                        <!-- Instance Status Panel -->
                        <div class="alert alert-info" role="alert" invisible="not instance_name">
                            <h5>
                                <i class="fa fa-whatsapp text-success"/>
                                WhatsApp Instance Status
                            </h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Instance Name:</strong> <field name="instance_name" readonly="1" nolabel="1"/>
                                </div>
                                <div class="col-md-6">
                                    <strong>Instance ID:</strong> <field name="instance_id" readonly="1" nolabel="1"/>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <strong>Status:</strong>
                                    <field name="instance_status" readonly="1" nolabel="1" widget="badge"
                                           decoration-success="instance_status == 'connected'"
                                           decoration-warning="instance_status in ['creating', 'waiting_qr', 'connecting']"
                                           decoration-danger="instance_status == 'error'"
                                           decoration-muted="instance_status == 'disconnected'"/>
                                </div>
                                <div class="col-md-6">
                                    <strong>Auto Reconnect:</strong>
                                    <field name="auto_reconnect" nolabel="1" widget="boolean_toggle"/>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <strong>Typing Indicator:</strong>
                                    <field name="enable_typing_indicator" nolabel="1" widget="boolean_toggle"/>
                                </div>
                            </div>
                        </div>

                        <!-- Setup Instructions -->
                        <div class="alert alert-warning" role="alert" invisible="instance_name">
                            <h5>
                                <i class="fa fa-exclamation-triangle"/>
                                WhatsApp Instance Not Configured
                            </h5>
                            <p>
                                You need to setup a WhatsApp instance to start sending messages.
                                Just enter your <strong>Evolution API Server URL</strong> and click <strong>"Setup Instance"</strong>.
                            </p>
                            <p>
                                <strong>What happens during setup:</strong>
                            </p>
                            <ol>
                                <li>A new WhatsApp instance will be created automatically</li>
                                <li>An API key will be generated for your instance</li>
                                <li>You'll get a QR code to scan with your phone</li>
                                <li>Once scanned, your instance will be connected and ready</li>
                            </ol>
                        </div>
                        
                        <notebook>
                            <page string="API Configuration" name="api_config" groups="whatsapp_evolution.group_whatsapp_user">
                                <group>
                                    <group name="server_config">
                                        <field name="server_url" placeholder="https://your-evolution-server.com" groups="whatsapp_evolution.group_whatsapp_user"/>
                                        <field name="global_api_key" password="True" placeholder="Enter your Evolution API global key" groups="whatsapp_evolution.group_whatsapp_user"/>
                                        <field name="api_key" placeholder="Auto-filled after instance creation" groups="whatsapp_evolution.group_whatsapp_user"/>
                                    </group>
                                    <group name="instance_config">
                                        <field name="instance_name" placeholder="Will be auto-generated" readonly="1" groups="whatsapp_evolution.group_whatsapp_user"/>
                                    </group>
                                </group>

                                <group string="Connection Test Results" name="test_results"
                                       invisible="connection_status == 'not_tested'">
                                    <field name="last_error_message" readonly="1"
                                           invisible="connection_status != 'error'"
                                           widget="text"/>
                                </group>
                            </page>

                            <page string="Instance Details" name="instance_details" invisible="not instance_name">
                                <group>
                                    <group name="instance_info">
                                        <field name="instance_name" readonly="1"/>
                                        <field name="instance_id" readonly="1"/>
                                        <field name="instance_status" readonly="1"/>
                                        <field name="last_connection_check" readonly="1"/>
                                    </group>
                                    <group name="instance_settings">
                                        <field name="auto_reconnect"/>
                                        <field name="enable_typing_indicator"/>
                                    </group>
                                </group>

                                <!-- QR Code Display (if available) -->
                                <group string="QR Code" name="qr_code_group" invisible="not qr_code_base64">
                                    <div class="text-center">
                                        <p>Scan this QR code with WhatsApp to connect:</p>
                                        <field name="qr_code_base64" widget="image"
                                               options="{'size': [300, 300]}"
                                               style="border: 1px solid #ddd; padding: 10px;"/>
                                        <br/>
                                        <p class="text-muted mt-2">
                                            Alternative pairing code: <strong><field name="pairing_code" readonly="1" nolabel="1"/></strong>
                                        </p>
                                    </div>
                                </group>

                                <!-- Hidden fields for QR code data -->
                                <group invisible="1">
                                    <field name="qr_code_data"/>
                                    <field name="qr_code_base64"/>
                                    <field name="pairing_code"/>
                                </group>
                            </page>
                            
                            <page string="Help" name="help">
                                <div class="alert alert-info" role="alert">
                                    <h4>
                                        <i class="fa fa-info-circle"/>
                                        WhatsApp Evolution API Setup Guide
                                    </h4>

                                    <h5>🚀 Quick Setup (Automated)</h5>
                                    <p><strong>New automated process - simplified setup!</strong></p>
                                    <ol>
                                        <li>Enter your Evolution API <strong>Server URL</strong></li>
                                        <li>Enter your <strong>Global API Key</strong> (required for authentication)</li>
                                        <li>Click <strong>"Setup Instance"</strong> button</li>
                                        <li>Scan the QR code with your WhatsApp mobile app</li>
                                        <li>Done! Your instance is ready to send messages</li>
                                    </ol>

                                    <hr/>

                                    <h5>📋 Field Descriptions</h5>
                                    <p><strong>Server URL:</strong> The base URL of your Evolution API server (e.g., https://your-server.com)</p>
                                    <p><strong>Global API Key:</strong> Your Evolution API global authentication key (required for creating instances)</p>
                                    <p><strong>Instance API Key:</strong> Instance-specific API key (automatically filled after instance creation)</p>
                                    <p><strong>Instance Name:</strong> Auto-generated unique name for your WhatsApp instance</p>

                                    <hr/>

                                    <h5>🔧 Instance Management</h5>
                                    <ul>
                                        <li><strong>Setup Instance:</strong> Create and connect a new WhatsApp instance</li>
                                        <li><strong>Reconnect:</strong> Reconnect if your WhatsApp gets disconnected</li>
                                        <li><strong>Refresh Status:</strong> Check current connection status</li>
                                        <li><strong>Disconnect:</strong> Manually disconnect the instance</li>
                                    </ul>

                                    <hr/>

                                    <h5>📱 Connection Status</h5>
                                    <ul>
                                        <li><span class="badge badge-secondary">Disconnected</span> - No instance configured</li>
                                        <li><span class="badge badge-warning">Creating</span> - Instance being created</li>
                                        <li><span class="badge badge-warning">Waiting QR</span> - Ready for QR scan</li>
                                        <li><span class="badge badge-warning">Connecting</span> - WhatsApp is connecting</li>
                                        <li><span class="badge badge-success">Connected</span> - Ready to send messages</li>
                                        <li><span class="badge badge-danger">Error</span> - Connection failed</li>
                                    </ul>

                                    <hr/>

                                    <p><strong>Need help?</strong> Visit: <a href="https://doc.evolution-api.com" target="_blank">Evolution API Documentation</a></p>
                                </div>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- WhatsApp Configuration List View -->
        <record id="view_whatsapp_config_tree" model="ir.ui.view">
            <field name="name">whatsapp.config.list</field>
            <field name="model">whatsapp.config</field>
            <field name="arch" type="xml">
                <list string="WhatsApp Configurations">
                    <field name="name"/>
                    <field name="server_url"/>
                    <field name="instance_name"/>
                    <field name="instance_status" widget="badge"
                           decoration-success="instance_status == 'connected'"
                           decoration-warning="instance_status in ['creating', 'waiting_qr', 'connecting']"
                           decoration-danger="instance_status == 'error'"
                           decoration-muted="instance_status == 'disconnected'"/>
                    <field name="connection_status" widget="badge"
                           decoration-success="connection_status == 'connected'"
                           decoration-danger="connection_status == 'error'"
                           decoration-muted="connection_status == 'not_tested'"/>
                    <field name="is_default" widget="boolean_toggle"/>
                    <field name="active" widget="boolean_toggle"/>
                    <field name="last_connection_check"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </list>
            </field>
        </record>

        <!-- WhatsApp Configuration Search View -->
        <record id="view_whatsapp_config_search" model="ir.ui.view">
            <field name="name">whatsapp.config.search</field>
            <field name="model">whatsapp.config</field>
            <field name="arch" type="xml">
                <search string="Search WhatsApp Configurations">
                    <field name="name"/>
                    <field name="server_url"/>
                    <field name="instance_name"/>
                    <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                    <filter string="Default" name="default" domain="[('is_default', '=', True)]"/>
                    <separator/>
                    <filter string="Instance Connected" name="instance_connected" domain="[('instance_status', '=', 'connected')]"/>
                    <filter string="Instance Disconnected" name="instance_disconnected" domain="[('instance_status', '=', 'disconnected')]"/>
                    <filter string="Instance Error" name="instance_error" domain="[('instance_status', '=', 'error')]"/>
                    <separator/>
                    <filter string="API Connected" name="api_connected" domain="[('connection_status', '=', 'connected')]"/>
                    <filter string="API Error" name="api_error" domain="[('connection_status', '=', 'error')]"/>
                    <group expand="0" string="Group By">
                        <filter string="Instance Status" name="group_instance_status" context="{'group_by': 'instance_status'}"/>
                        <filter string="Connection Status" name="group_status" context="{'group_by': 'connection_status'}"/>
                        <filter string="Company" name="group_company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- WhatsApp Configuration Action -->
        <record id="action_whatsapp_config" model="ir.actions.act_window">
            <field name="name">WhatsApp Configurations</field>
            <field name="res_model">whatsapp.config</field>
            <field name="view_mode">list,form</field>
            <field name="search_view_id" ref="view_whatsapp_config_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first WhatsApp configuration!
                </p>
                <p>
                    Configure your Evolution API server settings to start sending WhatsApp messages from Odoo.
                </p>
            </field>
        </record>

    </data>
</odoo>
