# WhatsApp Evolution API Integration - Installation Guide

## Quick Start

### 1. Install the Module

The module is already in your `custom_addons` directory. To install it:

1. **Update App List**:
   - Go to Apps in Odoo
   - Click "Update Apps List"
   - Search for "WhatsApp Evolution"
   - Click "Install"

2. **Or via Command Line**:
   ```bash
   # Restart Odoo with update
   ./odoo-bin -u whatsapp_evolution -d your_database_name
   ```

### 2. Set Up Evolution API

Before using the module, you need a running Evolution API server:

#### Option A: Docker (Recommended for Testing)
```bash
docker run -d \
    --name evolution_api \
    -p 8080:8080 \
    -e AUTHENTICATION_API_KEY=your-secret-key \
    atendai/evolution-api:latest
```

#### Option B: Full Installation
Follow the [Evolution API documentation](https://doc.evolution-api.com/v1/pt/get-started/introduction) for complete installation.

### 3. Configure WhatsApp Instance in Evolution API

1. **Access Evolution API Manager**: http://localhost:8080/manager
2. **Create Instance**: Create a new WhatsApp instance
3. **Connect WhatsApp**: Scan QR code to connect your WhatsApp
4. **Note Instance Name**: Remember the instance name you created

### 4. Configure Odoo Module

1. **Access Configuration**:
   - Go to **WhatsApp > Configuration > WhatsApp Settings**
   - Or **Settings > Technical > WhatsApp Evolution > WhatsApp Configurations**

2. **Edit Default Configuration**:
   - **Name**: Give it a descriptive name
   - **Server URL**: `http://localhost:8080` (or your server URL)
   - **API Key**: The key you set in Evolution API
   - **Instance Name**: The instance name from Evolution API
   - **Active**: Check this box
   - **Default Configuration**: Check this box

3. **Test Connection**:
   - Click "Test Connection" button
   - Should show "Connection to Evolution API successful!"

### 5. Send Your First Message

1. **Send Test Message**:
   - In the configuration, click "Send Test Message"
   - Enter a phone number with country code (e.g., +**********)
   - Click "Send Message"

2. **Or via Menu**:
   - Go to **WhatsApp > Messages > Send Message**
   - Fill in the details and send

## User Permissions

### Assign User Groups

1. **Go to Settings > Users & Companies > Users**
2. **Edit a user**
3. **In the Access Rights tab**, assign:
   - **WhatsApp User**: Can send messages and view logs
   - **WhatsApp Manager**: Can configure settings

## Troubleshooting

### Common Issues

1. **"Connection Error" when testing**:
   - Check if Evolution API is running: `curl http://localhost:8080`
   - Verify API key matches
   - Check firewall settings

2. **"Failed to send message"**:
   - Ensure WhatsApp instance is connected in Evolution API
   - Check phone number format (must include country code)
   - Verify instance name is correct

3. **Module not appearing**:
   - Update app list in Odoo
   - Check if module is in correct directory
   - Restart Odoo server

### Debug Steps

1. **Check Odoo Logs**:
   ```bash
   tail -f /var/log/odoo/odoo.log
   ```

2. **Test Evolution API Directly**:
   ```bash
   curl -X POST http://localhost:8080/message/sendText/your_instance \
     -H "Content-Type: application/json" \
     -H "apikey: your-api-key" \
     -d '{"number": "**********", "text": "Test message"}'
   ```

3. **Enable Developer Mode**:
   - Settings > Activate Developer Mode
   - Access technical menus for detailed logs

## Next Steps

- **Explore Message Log**: View all sent messages and their status
- **Set Up Multiple Configurations**: For different WhatsApp accounts
- **Integrate with Other Modules**: Use the API to send automated messages
- **Customize**: Extend the module for your specific needs

## API Usage Example

```python
# In your custom code
config = self.env['whatsapp.config'].get_default_config()
if config:
    config.send_message('+**********', 'Hello from Odoo!')
```

---

**Need Help?** 
- Check the [README.md](README.md) for detailed documentation
- Review Evolution API docs: https://doc.evolution-api.com
- Check Odoo logs for error details
