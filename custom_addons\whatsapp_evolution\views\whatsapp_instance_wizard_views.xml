<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- WhatsApp Instance Setup Wizard Form View -->
    <record id="view_whatsapp_instance_wizard_form" model="ir.ui.view">
        <field name="name">whatsapp.instance.wizard.form</field>
        <field name="model">whatsapp.instance.wizard</field>
        <field name="arch" type="xml">
            <form string="Setup WhatsApp Instance">
                <header>
                    <!-- Progress Bar -->
                    <div class="o_progressbar">
                        <div class="o_progressbar_value" style="width: 20%"></div>
                    </div>
                    
                    <!-- State-specific buttons -->
                    <button name="action_start_setup" 
                            string="Start Setup" 
                            type="object" 
                            class="btn-primary"
                            invisible="state != 'welcome'"/>
                    
                    <button name="action_check_connection" 
                            string="Check Connection" 
                            type="object" 
                            class="btn-primary"
                            invisible="state not in ['qr_display', 'connecting']"/>
                    
                    <button name="action_refresh_qr" 
                            string="Refresh QR Code" 
                            type="object" 
                            class="btn-secondary"
                            invisible="state != 'qr_display'"/>
                    
                    <button name="action_finish" 
                            string="Finish" 
                            type="object" 
                            class="btn-primary"
                            invisible="state != 'success'"/>
                    
                    <button name="action_cancel_setup" 
                            string="Cancel" 
                            type="object" 
                            class="btn-secondary"/>
                </header>
                
                <sheet>
                    <!-- Welcome State -->
                    <div invisible="state != 'welcome'">
                        <div class="text-center">
                            <h1>
                                <i class="fa fa-whatsapp text-success" style="font-size: 3em;"/>
                            </h1>
                            <h2>Setup WhatsApp Instance</h2>
                            <p class="text-muted">
                                This wizard will help you create and connect a WhatsApp instance 
                                to your Odoo system using Evolution API.
                            </p>
                        </div>
                        
                        <group>
                            <field name="config_id" readonly="1"/>
                            <field name="instance_name" 
                                   placeholder="e.g., odoo_company_001"
                                   invisible="is_reconnection"/>
                            <field name="is_reconnection" invisible="1"/>
                        </group>
                        
                        <div class="alert alert-info" role="alert" invisible="not is_reconnection">
                            <strong>Reconnection Mode:</strong> 
                            This will reconnect your existing WhatsApp instance.
                        </div>
                    </div>
                    
                    <!-- Creating State -->
                    <div invisible="state != 'creating'">
                        <div class="text-center">
                            <h2>
                                <i class="fa fa-spinner fa-spin text-primary"/>
                                Creating Instance...
                            </h2>
                            <p class="text-muted">Please wait while we create your WhatsApp instance.</p>
                        </div>
                    </div>
                    
                    <!-- QR Code Display State -->
                    <div invisible="state != 'qr_display'">
                        <div class="text-center">
                            <h2>Scan QR Code</h2>
                            <p class="text-muted">
                                Open WhatsApp on your phone and scan this QR code to connect.
                            </p>
                            
                            <!-- QR Code Image -->
                            <div invisible="not qr_code_base64">
                                <field name="qr_code_base64" widget="image"
                                       options="{'size': [300, 300]}"
                                       style="border: 1px solid #ddd; padding: 10px;"/>
                            </div>

                            <!-- Fallback: Pairing Code -->
                            <div invisible="not pairing_code or qr_code_base64">
                                <h4>Alternative: Use Pairing Code</h4>
                                <div class="alert alert-info">
                                    <strong>Pairing Code: <field name="pairing_code" readonly="1" nolabel="1"/></strong>
                                    <br/>
                                    Enter this code in WhatsApp → Settings → Linked Devices → Link a Device
                                </div>
                            </div>
                            
                            <!-- Instructions -->
                            <div class="mt-3">
                                <h5>How to scan:</h5>
                                <ol class="text-left" style="display: inline-block;">
                                    <li>Open WhatsApp on your phone</li>
                                    <li>Go to Settings → Linked Devices</li>
                                    <li>Tap "Link a Device"</li>
                                    <li>Scan this QR code</li>
                                </ol>
                            </div>
                        </div>
                        
                        <group>
                            <field name="connection_attempts" readonly="1"/>
                            <field name="max_connection_attempts" readonly="1"/>
                        </group>
                    </div>
                    
                    <!-- Connecting State -->
                    <div invisible="state != 'connecting'">
                        <div class="text-center">
                            <h2>
                                <i class="fa fa-spinner fa-spin text-primary"/>
                                Connecting...
                            </h2>
                            <p class="text-muted">
                                WhatsApp is connecting to your instance. Please wait...
                            </p>
                        </div>
                    </div>
                    
                    <!-- Success State -->
                    <div invisible="state != 'success'">
                        <div class="text-center">
                            <h1>
                                <i class="fa fa-check-circle text-success" style="font-size: 3em;"/>
                            </h1>
                            <h2>Success!</h2>
                            <p class="text-success">
                                Your WhatsApp instance is now connected and ready to use.
                            </p>
                            <div class="alert alert-success" role="alert">
                                <strong>What's next?</strong>
                                <ul class="text-left" style="display: inline-block;">
                                    <li>You can now send WhatsApp messages from Odoo</li>
                                    <li>Configure business templates for automated messaging</li>
                                    <li>Monitor your connection status in the configuration</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Error State -->
                    <div invisible="state != 'error'">
                        <div class="text-center">
                            <h1>
                                <i class="fa fa-exclamation-triangle text-danger" style="font-size: 3em;"/>
                            </h1>
                            <h2>Setup Failed</h2>
                            <div class="alert alert-danger" role="alert">
                                <field name="error_message" readonly="1"/>
                            </div>
                            <p class="text-muted">
                                Please check your Evolution API configuration and try again.
                            </p>
                        </div>
                    </div>
                    
                    <!-- Status Message (shown in all states) -->
                    <div invisible="not status_message">
                        <div class="alert alert-info text-center" role="alert">
                            <field name="status_message" readonly="1"/>
                        </div>
                    </div>
                    
                    <!-- Hidden fields -->
                    <group invisible="1">
                        <field name="state"/>
                        <field name="qr_code_data"/>
                        <field name="qr_code_base64"/>
                        <field name="pairing_code"/>
                        <field name="progress_percentage"/>
                        <field name="auto_refresh"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- WhatsApp Instance Setup Wizard Action -->
    <record id="action_whatsapp_instance_wizard" model="ir.actions.act_window">
        <field name="name">Setup WhatsApp Instance</field>
        <field name="res_model">whatsapp.instance.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="view_id" ref="view_whatsapp_instance_wizard_form"/>
    </record>
</odoo>
