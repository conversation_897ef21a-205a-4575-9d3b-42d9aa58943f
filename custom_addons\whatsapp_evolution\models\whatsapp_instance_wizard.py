# -*- coding: utf-8 -*-

import logging
import time
from odoo import models, fields, api, _
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class WhatsAppInstanceWizard(models.TransientModel):
    _name = 'whatsapp.instance.wizard'
    _description = 'WhatsApp Instance Setup Wizard'

    # Configuration reference
    config_id = fields.Many2one(
        'whatsapp.config',
        string='WhatsApp Configuration',
        required=True
    )
    
    # Wizard state management
    state = fields.Selection([
        ('welcome', 'Welcome'),
        ('creating', 'Creating Instance'),
        ('qr_display', 'QR Code Display'),
        ('connecting', 'Connecting'),
        ('success', 'Success'),
        ('error', 'Error')
    ], string='Wizard State', default='welcome')
    
    # Setup type
    is_reconnection = fields.Boolean(
        string='Is Reconnection',
        default=False,
        help='Whether this is a reconnection or new setup'
    )
    
    # Instance information
    instance_name = fields.Char(
        string='Instance Name',
        help='Name for the WhatsApp instance'
    )
    
    # QR Code information
    qr_code_data = fields.Text(
        string='QR Code Data',
        readonly=True
    )
    
    qr_code_base64 = fields.Text(
        string='QR Code Image',
        readonly=True
    )
    
    pairing_code = fields.Char(
        string='Pairing Code',
        readonly=True
    )
    
    # Status and messages
    status_message = fields.Text(
        string='Status Message',
        readonly=True
    )
    
    error_message = fields.Text(
        string='Error Message',
        readonly=True
    )
    
    # Progress tracking
    progress_percentage = fields.Integer(
        string='Progress',
        default=0,
        readonly=True
    )
    
    # Auto-refresh for connection monitoring
    auto_refresh = fields.Boolean(
        string='Auto Refresh',
        default=True,
        help='Automatically refresh to check connection status'
    )
    
    connection_attempts = fields.Integer(
        string='Connection Attempts',
        default=0,
        readonly=True
    )
    
    max_connection_attempts = fields.Integer(
        string='Max Connection Attempts',
        default=60,  # 5 minutes with 5-second intervals
        readonly=True
    )

    @api.model
    def default_get(self, fields_list):
        """Set default values based on context"""
        defaults = super().default_get(fields_list)
        
        config_id = self.env.context.get('default_config_id')
        is_reconnection = self.env.context.get('default_is_reconnection', False)
        
        if config_id:
            config = self.env['whatsapp.config'].browse(config_id)
            defaults.update({
                'config_id': config_id,
                'is_reconnection': is_reconnection,
                'instance_name': config.instance_name or config._generate_instance_name(),
            })
            
            # If it's a reconnection and QR code already exists, show it
            if is_reconnection and config.qr_code_data:
                defaults.update({
                    'state': 'qr_display',
                    'qr_code_data': config.qr_code_data,
                    'qr_code_base64': config.qr_code_base64,
                    'pairing_code': config.pairing_code,
                    'status_message': _('Please scan the QR code with your WhatsApp mobile app.'),
                    'progress_percentage': 50,
                })
        
        return defaults

    def action_start_setup(self):
        """Start the instance setup process"""
        self.ensure_one()
        
        try:
            self.state = 'creating'
            self.status_message = _('Creating WhatsApp instance...')
            self.progress_percentage = 25
            
            # Update config with instance name
            if self.instance_name:
                self.config_id.instance_name = self.instance_name
            
            # Create the instance (QR code is included in the response)
            if not self.is_reconnection:
                self.config_id.create_evolution_instance()
            else:
                # For reconnection, we need to get QR code separately
                self.config_id.get_qr_code()
            
            # Update wizard with QR code data
            self.qr_code_data = self.config_id.qr_code_data
            self.qr_code_base64 = self.config_id.qr_code_base64
            self.pairing_code = self.config_id.pairing_code
            
            self.state = 'qr_display'
            self.status_message = _('Please scan the QR code with your WhatsApp mobile app.')
            self.progress_percentage = 50
            
            return self._return_wizard_action()
            
        except Exception as e:
            self.state = 'error'
            self.error_message = str(e)
            self.status_message = _('Failed to setup instance: %s') % str(e)
            return self._return_wizard_action()

    def action_check_connection(self):
        """Check if WhatsApp has been connected"""
        self.ensure_one()
        
        try:
            self.connection_attempts += 1
            
            # Check connection status
            is_connected = self.config_id.check_connection_status()
            
            if is_connected:
                # Try to fetch the instance-specific API key
                try:
                    self.config_id.fetch_instance_api_key()
                    success_message = _('WhatsApp instance is now connected and ready to use. Instance API key has been automatically configured.')
                except Exception as e:
                    _logger.warning(f"Could not fetch API key automatically: {str(e)}")
                    success_message = _('WhatsApp instance is connected, but could not fetch instance API key automatically. You can fetch it manually using the "Fetch API Key" button.')

                self.state = 'success'
                self.status_message = _('WhatsApp instance connected successfully!')
                self.progress_percentage = 100
                self.auto_refresh = False

                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Success!'),
                        'message': success_message,
                        'type': 'success',
                    }
                }
            
            elif self.connection_attempts >= self.max_connection_attempts:
                self.state = 'error'
                self.error_message = _('Connection timeout. Please try again.')
                self.status_message = _('Failed to connect within the time limit.')
                self.auto_refresh = False
                
            else:
                # Update progress
                progress = 50 + (self.connection_attempts / self.max_connection_attempts * 40)
                self.progress_percentage = min(int(progress), 90)
                
                if self.config_id.instance_status == 'connecting':
                    self.status_message = _('Connecting... Please wait.')
                else:
                    self.status_message = _('Waiting for QR code scan... (%d/%d)') % (
                        self.connection_attempts, self.max_connection_attempts
                    )
            
            return self._return_wizard_action()
            
        except Exception as e:
            self.state = 'error'
            self.error_message = str(e)
            self.status_message = _('Error checking connection: %s') % str(e)
            self.auto_refresh = False
            return self._return_wizard_action()

    def action_refresh_qr(self):
        """Refresh the QR code"""
        self.ensure_one()
        
        try:
            self.config_id.get_qr_code()
            
            # Update wizard with new QR code data
            self.qr_code_data = self.config_id.qr_code_data
            self.qr_code_base64 = self.config_id.qr_code_base64
            self.pairing_code = self.config_id.pairing_code
            self.connection_attempts = 0
            
            self.status_message = _('QR code refreshed. Please scan with your WhatsApp mobile app.')
            
            return self._return_wizard_action()
            
        except Exception as e:
            self.state = 'error'
            self.error_message = str(e)
            self.status_message = _('Failed to refresh QR code: %s') % str(e)
            return self._return_wizard_action()

    def action_cancel_setup(self):
        """Cancel the setup process"""
        self.ensure_one()
        
        # If instance was created but not connected, clean it up
        if (not self.is_reconnection and 
            self.config_id.instance_name and 
            self.config_id.instance_status != 'connected'):
            
            try:
                self.config_id.delete_evolution_instance()
            except:
                pass  # Ignore cleanup errors
        
        return {'type': 'ir.actions.act_window_close'}

    def action_finish(self):
        """Finish the setup process"""
        self.ensure_one()
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Setup Complete'),
                'message': _('WhatsApp instance is ready to use!'),
                'type': 'success',
            }
        }

    def _return_wizard_action(self):
        """Return action to reload the wizard"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Setup WhatsApp Instance'),
            'res_model': 'whatsapp.instance.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
        }
