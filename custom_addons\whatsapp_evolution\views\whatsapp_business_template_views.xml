<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- WhatsApp Business Template Form View -->
        <record id="view_whatsapp_business_template_form" model="ir.ui.view">
            <field name="name">whatsapp.business.template.form</field>
            <field name="model">whatsapp.business.template</field>
            <field name="arch" type="xml">
                <form string="WhatsApp Business Template">
                    <header>
                        <button name="action_send_test_message"
                                string="Send Test Message"
                                type="object"
                                class="btn-primary"
                                invisible="active == False"/>
                        <field name="active" widget="boolean_button" 
                               options="{'terminology': {'string_true': 'Active', 'string_false': 'Inactive'}}"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_sent_messages"
                                    string="View Messages"
                                    type="object"
                                    class="oe_stat_button"
                                    icon="fa-envelope"
                                    invisible="active == False">
                                <field name="total_sent" widget="statinfo" string="Messages Sent"/>
                            </button>
                        </div>

                        <div class="oe_title">
                            <h1>
                                <field name="template_name" readonly="1"/>
                            </h1>
                            <h3>
                                <field name="template_category" widget="badge" readonly="1"/>
                            </h3>
                        </div>

                        <group>
                            <group name="template_info" string="Template Information">
                                <field name="template_code" readonly="1"/>
                                <field name="template_description" readonly="1"/>
                                <field name="trigger_type" readonly="1"/>
                                <field name="schedule_frequency" readonly="1"
                                       invisible="trigger_type != 'scheduled'"/>
                                <field name="last_sent_date" readonly="1"/>
                            </group>
                            <group name="configuration" string="Configuration">
                                <field name="config_id" 
                                       domain="[('company_id', '=', company_id)]"
                                       context="{'default_company_id': company_id}"/>
                                <field name="send_time"
                                       invisible="trigger_type != 'scheduled'"
                                       widget="float_time"/>
                                <field name="threshold_value"
                                       invisible="trigger_type != 'threshold'"/>
                                <field name="threshold_operator"
                                       invisible="trigger_type != 'threshold'"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                            </group>
                        </group>

                        <notebook>
                            <page string="Message Content" name="message_content">
                                <group>
                                    <field name="available_variables" readonly="1" 
                                           string="Available Variables" 
                                           widget="text"/>
                                </group>
                                <group string="Default Message Template">
                                    <field name="default_message" readonly="1" widget="text" nolabel="1"/>
                                </group>
                                <group string="Custom Message">
                                    <field name="custom_message" widget="text" nolabel="1" 
                                           placeholder="Leave empty to use default message, or customize here..."/>
                                </group>
                            </page>
                            <page string="Recipients" name="recipients">
                                <group>
                                    <field name="recipients" widget="text" nolabel="1" 
                                           placeholder="Enter phone numbers (one per line) with country code:&#10;+1234567890&#10;+9876543210&#10;&#10;Use # for comments:&#10;# Manager&#10;+1234567890"/>
                                </group>
                                <div class="alert alert-info" role="alert">
                                    <strong>Phone Number Format:</strong>
                                    <ul>
                                        <li>Include country code (e.g., +1234567890)</li>
                                        <li>One phone number per line</li>
                                        <li>Use # at the beginning of a line for comments</li>
                                        <li>Remove spaces and special characters</li>
                                    </ul>
                                </div>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- WhatsApp Business Template List View -->
        <record id="view_whatsapp_business_template_list" model="ir.ui.view">
            <field name="name">whatsapp.business.template.list</field>
            <field name="model">whatsapp.business.template</field>
            <field name="arch" type="xml">
                <list string="WhatsApp Business Templates" create="false" delete="false">
                    <field name="sequence" widget="handle"/>
                    <field name="template_name"/>
                    <field name="template_category" widget="badge"/>
                    <field name="trigger_type" widget="badge"/>
                    <field name="schedule_frequency"/>
                    <field name="active" widget="boolean_toggle"/>
                    <field name="total_sent"/>
                    <field name="last_sent_date"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <button name="action_send_test_message"
                            string="Test"
                            type="object"
                            icon="fa-paper-plane"
                            invisible="active == False"/>
                </list>
            </field>
        </record>

        <!-- WhatsApp Business Template Search View -->
        <record id="view_whatsapp_business_template_search" model="ir.ui.view">
            <field name="name">whatsapp.business.template.search</field>
            <field name="model">whatsapp.business.template</field>
            <field name="arch" type="xml">
                <search string="Search WhatsApp Templates">
                    <field name="template_name"/>
                    <field name="template_code"/>
                    <field name="template_category"/>
                    <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                    <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                    <separator/>
                    <filter string="Scheduled" name="scheduled" domain="[('trigger_type', '=', 'scheduled')]"/>
                    <filter string="Event-Based" name="event" domain="[('trigger_type', '=', 'event')]"/>
                    <filter string="Threshold-Based" name="threshold" domain="[('trigger_type', '=', 'threshold')]"/>
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter string="Category" name="group_category" context="{'group_by': 'template_category'}"/>
                        <filter string="Trigger Type" name="group_trigger" context="{'group_by': 'trigger_type'}"/>
                        <filter string="Status" name="group_active" context="{'group_by': 'active'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- WhatsApp Template Test Wizard Form View -->
        <record id="view_whatsapp_template_test_wizard_form" model="ir.ui.view">
            <field name="name">whatsapp.template.test.wizard.form</field>
            <field name="model">whatsapp.template.test.wizard</field>
            <field name="arch" type="xml">
                <form string="Send Test Message">
                    <sheet>
                        <group>
                            <field name="template_id" readonly="1"/>
                            <field name="phone_number" placeholder="+1234567890"/>
                        </group>
                        <group string="Test Variables">
                            <field name="test_variables" widget="text" nolabel="1" 
                                   placeholder="product_name=Test Product&#10;current_qty=5&#10;min_qty=10&#10;uom=Units&#10;location_name=Main Warehouse"/>
                        </group>
                        <group string="Message Preview">
                            <field name="preview_message" widget="text" readonly="1" nolabel="1"/>
                        </group>
                    </sheet>
                    <footer>
                        <button name="action_send_test_message" string="Send Test Message" type="object" class="btn-primary"/>
                        <button name="action_send_and_close" string="Send &amp; Close" type="object" class="btn-secondary"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <!-- Actions -->
        <record id="action_whatsapp_business_template" model="ir.actions.act_window">
            <field name="name">WhatsApp Business Templates</field>
            <field name="res_model">whatsapp.business.template</field>
            <field name="view_mode">list,form</field>
            <field name="context">{}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Configure WhatsApp Business Message Templates
                </p>
                <p>
                    Set up automated WhatsApp messages for your business processes like:
                    <ul>
                        <li>Low stock alerts</li>
                        <li>Sales performance reports</li>
                        <li>Customer notifications</li>
                        <li>Financial summaries</li>
                    </ul>
                </p>
            </field>
        </record>

    </data>
</odoo>
