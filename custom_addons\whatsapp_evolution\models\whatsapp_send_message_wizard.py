# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class WhatsAppSendMessageWizard(models.TransientModel):
    _name = 'whatsapp.send.message.wizard'
    _description = 'Send WhatsApp Message Wizard'

    config_id = fields.Many2one(
        'whatsapp.config',
        string='WhatsApp Configuration',
        required=True,
        domain=[('active', '=', True)]
    )
    
    phone_number = fields.Char(
        string='Phone Number',
        required=True,
        help='Phone number with country code (e.g., +1234567890)'
    )
    
    message_text = fields.Text(
        string='Message',
        required=True,
        help='The message to send via WhatsApp'
    )
    
    delay = fields.Integer(
        string='Delay (milliseconds)',
        help='Delay before sending the message in milliseconds',
        default=0
    )
    
    is_test = fields.Boolean(
        string='Test Message',
        default=False,
        help='Whether this is a test message'
    )

    @api.model
    def default_get(self, fields_list):
        """Set default configuration"""
        res = super().default_get(fields_list)
        
        if 'config_id' in fields_list and not res.get('config_id'):
            config = self.env['whatsapp.config'].get_default_config()
            if config:
                res['config_id'] = config.id
        
        if 'message_text' in fields_list and not res.get('message_text'):
            if res.get('is_test'):
                res['message_text'] = 'Hello! This is a test message from Odoo WhatsApp Evolution integration. 🚀'
        
        return res

    def action_send_message(self):
        """Send the WhatsApp message"""
        self.ensure_one()
        
        if not self.config_id:
            raise UserError(_('Please select a WhatsApp configuration.'))
        
        if not self.phone_number:
            raise UserError(_('Please enter a phone number.'))
        
        if not self.message_text:
            raise UserError(_('Please enter a message.'))
        
        try:
            # Send the message
            response_data = self.config_id.send_message(
                self.phone_number,
                self.message_text,
                self.delay if self.delay > 0 else None
            )
            
            # Show success notification
            message_type = 'test message' if self.is_test else 'message'
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _('WhatsApp %s sent successfully to %s!') % (message_type, self.phone_number),
                    'type': 'success',
                    'sticky': False,
                }
            }
            
        except Exception as e:
            # Show error notification
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Failed to send WhatsApp message: %s') % str(e),
                    'type': 'danger',
                    'sticky': True,
                }
            }

    def action_send_and_close(self):
        """Send message and close wizard"""
        result = self.action_send_message()
        
        # If successful, close the wizard
        if result.get('params', {}).get('type') == 'success':
            return {'type': 'ir.actions.act_window_close'}
        
        return result
