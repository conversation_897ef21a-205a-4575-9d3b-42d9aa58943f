<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- WhatsApp Message Form View -->
        <record id="view_whatsapp_message_form" model="ir.ui.view">
            <field name="name">whatsapp.message.form</field>
            <field name="model">whatsapp.message</field>
            <field name="arch" type="xml">
                <form string="WhatsApp Message" create="false">
                    <header>
                        <button name="action_resend_message" type="object" string="Resend Message" 
                                class="btn-primary" icon="fa-refresh"
                                invisible="status == 'sent'"/>
                        <button name="action_view_response_data" type="object" string="View Response" 
                                class="btn-secondary" icon="fa-eye"
                                invisible="not response_data"/>
                        <field name="status" widget="badge" 
                               decoration-success="status == 'sent'"
                               decoration-danger="status == 'failed'"
                               decoration-warning="status == 'pending'"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1><field name="display_name" readonly="1"/></h1>
                        </div>
                        
                        <group>
                            <group name="message_info">
                                <field name="config_id" readonly="1"/>
                                <field name="template_id" readonly="1"/>
                                <field name="phone_number" readonly="1"/>
                                <field name="sent_date" readonly="1"/>
                                <field name="status" readonly="1"/>
                            </group>
                            <group name="response_info">
                                <field name="message_id" readonly="1"/>
                                <field name="remote_jid" readonly="1"/>
                                <field name="company_id" readonly="1" groups="base.group_multi_company"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Message Content" name="content">
                                <field name="message_text" readonly="1" widget="text"/>
                            </page>
                            
                            <page string="Response Data" name="response" invisible="not response_data">
                                <field name="response_data" readonly="1" widget="text"/>
                            </page>
                            
                            <page string="Error Details" name="error" invisible="not error_message">
                                <field name="error_message" readonly="1" widget="text"/>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- WhatsApp Message List View -->
        <record id="view_whatsapp_message_tree" model="ir.ui.view">
            <field name="name">whatsapp.message.list</field>
            <field name="model">whatsapp.message</field>
            <field name="arch" type="xml">
                <list string="WhatsApp Messages" create="false">
                    <field name="sent_date"/>
                    <field name="template_id"/>
                    <field name="phone_number"/>
                    <field name="message_text" string="Message" widget="text" limit="50"/>
                    <field name="config_id"/>
                    <field name="status" widget="badge"
                           decoration-success="status == 'sent'"
                           decoration-danger="status == 'failed'"
                           decoration-warning="status == 'pending'"/>
                    <field name="message_id"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </list>
            </field>
        </record>

        <!-- WhatsApp Message Search View -->
        <record id="view_whatsapp_message_search" model="ir.ui.view">
            <field name="name">whatsapp.message.search</field>
            <field name="model">whatsapp.message</field>
            <field name="arch" type="xml">
                <search string="Search WhatsApp Messages">
                    <field name="phone_number"/>
                    <field name="message_text"/>
                    <field name="config_id"/>
                    <filter string="Sent" name="sent" domain="[('status', '=', 'sent')]"/>
                    <filter string="Failed" name="failed" domain="[('status', '=', 'failed')]"/>
                    <filter string="Pending" name="pending" domain="[('status', '=', 'pending')]"/>
                    <separator/>
                    <filter string="Today" name="today" domain="[('sent_date', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0)))]"/>
                    <filter string="This Week" name="this_week" domain="[('sent_date', '&gt;=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                    <filter string="This Month" name="this_month" domain="[('sent_date', '&gt;=', (context_today().replace(day=1)).strftime('%Y-%m-%d'))]"/>
                    <group expand="0" string="Group By">
                        <filter string="Status" name="group_status" context="{'group_by': 'status'}"/>
                        <filter string="Configuration" name="group_config" context="{'group_by': 'config_id'}"/>
                        <filter string="Date" name="group_date" context="{'group_by': 'sent_date:day'}"/>
                        <filter string="Company" name="group_company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- WhatsApp Message Action -->
        <record id="action_whatsapp_message" model="ir.actions.act_window">
            <field name="name">WhatsApp Messages</field>
            <field name="res_model">whatsapp.message</field>
            <field name="view_mode">list,form</field>
            <field name="search_view_id" ref="view_whatsapp_message_search"/>
            <field name="context">{'search_default_today': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No WhatsApp messages found!
                </p>
                <p>
                    WhatsApp messages sent through Evolution API will appear here.
                    Use the WhatsApp configuration to send your first message.
                </p>
            </field>
        </record>

    </data>
</odoo>
