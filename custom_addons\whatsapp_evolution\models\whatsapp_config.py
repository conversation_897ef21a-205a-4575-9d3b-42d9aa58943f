# -*- coding: utf-8 -*-

import requests
import json
import logging
import uuid
import time
from datetime import datetime, timedelta
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class WhatsAppConfig(models.Model):
    _name = 'whatsapp.config'
    _description = 'WhatsApp Evolution API Configuration'
    _rec_name = 'name'

    name = fields.Char(
        string='Configuration Name',
        required=True,
        default='Default WhatsApp Config'
    )
    
    server_url = fields.Char(
        string='Evolution API Server URL',
        required=True,
        help='Evolution API server URL (e.g., https://your-server.com)',
        default=lambda self: self._get_default_server_url()
    )

    global_api_key = fields.Char(
        string='Global API Key',
        required=True,
        help='Global API key for Evolution API authentication (required for creating instances)',
        default=lambda self: self._get_default_global_api_key()
    )

    api_key = fields.Char(
        string='Instance API Key',
        help='Instance-specific API key (automatically set after instance creation)',
        readonly=True
    )
    
    instance_name = fields.Char(
        string='Instance Name',
        help='WhatsApp instance name in Evolution API'
    )

    # New Evolution API Instance Management Fields
    instance_id = fields.Char(
        string='Instance ID',
        readonly=True,
        help='Unique instance identifier from Evolution API'
    )

    instance_status = fields.Selection([
        ('disconnected', 'Disconnected'),
        ('creating', 'Creating Instance'),
        ('waiting_qr', 'Waiting for QR Scan'),
        ('connecting', 'Connecting'),
        ('connected', 'Connected'),
        ('error', 'Error')
    ], string='Instance Status', default='disconnected', readonly=True)

    qr_code_data = fields.Text(
        string='QR Code Data',
        readonly=True,
        help='QR code data for WhatsApp connection'
    )

    qr_code_base64 = fields.Text(
        string='QR Code Base64',
        readonly=True,
        help='QR code image in base64 format'
    )

    pairing_code = fields.Char(
        string='Pairing Code',
        readonly=True,
        help='Alternative pairing code for connection'
    )

    last_connection_check = fields.Datetime(
        string='Last Connection Check',
        readonly=True
    )

    auto_reconnect = fields.Boolean(
        string='Auto Reconnect',
        default=True,
        help='Automatically attempt to reconnect when disconnected'
    )

    enable_typing_indicator = fields.Boolean(
        string='Enable Typing Indicator',
        default=True,
        help='Send typing indicator before messages to simulate human behavior and reduce ban risk'
    )

    active = fields.Boolean(
        string='Active',
        default=True,
        help='Whether this configuration is active'
    )

    is_default = fields.Boolean(
        string='Default Configuration',
        default=False,
        help='Use this as the default configuration for sending messages'
    )

    connection_status = fields.Selection([
        ('not_tested', 'Not Tested'),
        ('connected', 'Connected'),
        ('error', 'Connection Error'),
    ], string='Connection Status', default='not_tested', readonly=True)
    
    last_test_date = fields.Datetime(
        string='Last Test Date',
        readonly=True
    )
    
    last_error_message = fields.Text(
        string='Last Error Message',
        readonly=True
    )
    
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.company,
        required=True
    )

    @api.model
    def _get_default_server_url(self):
        """Get the default server URL from settings"""
        return self.env['ir.config_parameter'].sudo().get_param(
            'whatsapp_evolution.default_server_url',
            'https://whatsapp-n8n-evolution.ufzryk.easypanel.host'
        )

    @api.model
    def _get_default_global_api_key(self):
        """Get the default global API key from settings"""
        return self.env['ir.config_parameter'].sudo().get_param(
            'whatsapp_evolution.default_global_api_key',
            '429683C4C977415CAAFCCE10F7D57E12'
        )

    @api.constrains('is_default')
    def _check_default_config(self):
        """Ensure only one default configuration per company"""
        for record in self:
            if record.is_default:
                other_defaults = self.search([
                    ('is_default', '=', True),
                    ('company_id', '=', record.company_id.id),
                    ('id', '!=', record.id)
                ])
                if other_defaults:
                    raise ValidationError(_('Only one default configuration is allowed per company.'))

    @api.model
    def get_default_config(self):
        """Get the default configuration for the current company"""
        config = self.search([
            ('is_default', '=', True),
            ('active', '=', True),
            ('company_id', '=', self.env.company.id)
        ], limit=1)
        
        if not config:
            config = self.search([
                ('active', '=', True),
                ('company_id', '=', self.env.company.id)
            ], limit=1)
        
        return config

    def test_connection(self):
        """Test connection to Evolution API"""
        self.ensure_one()
        
        try:
            # Test basic API connectivity by checking the root endpoint
            url = f"{self.server_url.rstrip('/')}"

            # Use global API key for testing
            api_key = self._get_global_api_key()
            if not api_key:
                raise UserError(_('Global API Key is required for testing connection.'))

            headers = {
                'Content-Type': 'application/json',
                'apikey': api_key
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                # Try to get instance connection status
                instance_url = f"{url}/instance/connectionState/{self.instance_name}"
                instance_response = requests.get(instance_url, headers=headers, timeout=10)
                
                self.connection_status = 'connected'
                self.last_test_date = fields.Datetime.now()
                self.last_error_message = False
                
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Success'),
                        'message': _('Connection to Evolution API successful!'),
                        'type': 'success',
                    }
                }
            else:
                raise Exception(f"HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            error_msg = str(e)
            self.connection_status = 'error'
            self.last_test_date = fields.Datetime.now()
            self.last_error_message = error_msg
            
            _logger.error(f"WhatsApp Evolution API connection test failed: {error_msg}")
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Connection Error'),
                    'message': _('Failed to connect to Evolution API: %s') % error_msg,
                    'type': 'danger',
                }
            }

    def send_message(self, phone_number, message_text, delay=None, enable_typing=True):
        """Send a WhatsApp message using Evolution API with human-like behavior"""
        self.ensure_one()

        if not self.active:
            raise UserError(_('This WhatsApp configuration is not active.'))

        # Ensure instance is connected
        self._ensure_instance_connected()

        # Clean phone number (remove spaces, dashes, etc.)
        clean_phone = ''.join(filter(str.isdigit, phone_number))

        # Ensure phone number has country code
        if not clean_phone.startswith('+'):
            if len(clean_phone) == 10:  # Assume local number, add default country code
                clean_phone = '+1' + clean_phone  # Default to US, adjust as needed
            elif len(clean_phone) == 11 and clean_phone.startswith('1'):
                clean_phone = '+' + clean_phone
            else:
                clean_phone = '+' + clean_phone

        # Remove + for Evolution API
        clean_phone = clean_phone.replace('+', '')

        # Send typing indicator before message (human-like behavior)
        if enable_typing and self.enable_typing_indicator:
            self._send_typing_indicator(clean_phone, message_text)
        
        url = f"{self.server_url.rstrip('/')}/message/sendText/{self.instance_name}"
        headers = {
            'Content-Type': 'application/json',
            'apikey': self.api_key
        }
        
        payload = {
            'number': clean_phone,
            'text': message_text
        }
        
        if delay:
            payload['delay'] = delay
        
        try:
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            response.raise_for_status()
            
            response_data = response.json()
            
            # Log the message
            self.env['whatsapp.message'].create({
                'config_id': self.id,
                'phone_number': phone_number,
                'message_text': message_text,
                'status': 'sent',
                'response_data': json.dumps(response_data),
                'sent_date': fields.Datetime.now(),
            })
            
            return response_data
            
        except requests.exceptions.RequestException as e:
            error_msg = str(e)
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_data = e.response.json()
                    error_msg = error_data.get('message', error_msg)
                except:
                    error_msg = e.response.text or error_msg
            
            # Log the failed message
            self.env['whatsapp.message'].create({
                'config_id': self.id,
                'phone_number': phone_number,
                'message_text': message_text,
                'status': 'failed',
                'error_message': error_msg,
                'sent_date': fields.Datetime.now(),
            })
            
            _logger.error(f"Failed to send WhatsApp message: {error_msg}")
            raise UserError(_('Failed to send WhatsApp message: %s') % error_msg)

    def _send_typing_indicator(self, clean_phone, message_text):
        """Send typing indicator before message to simulate human behavior"""
        try:
            # Calculate realistic typing duration based on message length
            word_count = len(message_text.split())
            # Assume 40-60 words per minute typing speed
            base_duration = word_count / 50 * 60  # 50 WPM average

            # Add randomness and set reasonable bounds (2-10 seconds)
            import random
            typing_duration = max(2, min(10, base_duration + random.uniform(-1, 2)))

            # Send typing presence
            presence_url = f"{self.server_url.rstrip('/')}/chat/sendPresence/{self.instance_name}"
            headers = {
                'Content-Type': 'application/json',
                'apikey': self.api_key
            }

            # Start typing
            payload = {
                'number': clean_phone,
                'presence': 'composing'
            }

            response = requests.post(presence_url, headers=headers, json=payload, timeout=10)
            if response.status_code == 200:
                _logger.info(f"Typing indicator sent for {clean_phone}, duration: {typing_duration:.1f}s")

                # Wait for typing duration
                time.sleep(typing_duration)

                # Set back to available (stop typing)
                payload['presence'] = 'available'
                requests.post(presence_url, headers=headers, json=payload, timeout=5)
            else:
                _logger.warning(f"Failed to send typing indicator: HTTP {response.status_code}")

        except Exception as e:
            # Don't fail the message if typing indicator fails
            _logger.warning(f"Typing indicator failed (non-critical): {e}")

    def action_send_test_message(self):
        """Send a test message"""
        self.ensure_one()

        return {
            'type': 'ir.actions.act_window',
            'name': _('Send Test WhatsApp Message'),
            'res_model': 'whatsapp.send.message.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_config_id': self.id,
                'default_is_test': True,
            }
        }

    # ========================================
    # Evolution API Instance Management Methods
    # ========================================

    def action_setup_instance(self):
        """Launch the instance setup wizard"""
        self.ensure_one()

        return {
            'type': 'ir.actions.act_window',
            'name': _('Setup WhatsApp Instance'),
            'res_model': 'whatsapp.instance.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_config_id': self.id,
            }
        }

    def action_reconnect_instance(self):
        """Reconnect existing instance"""
        self.ensure_one()

        if not self.instance_name:
            raise UserError(_('No instance name configured. Please setup a new instance.'))

        return self._start_reconnection_process()

    def _generate_instance_name(self):
        """Generate a unique instance name"""
        company_name = self.company_id.name.lower().replace(' ', '_')
        timestamp = int(time.time())
        unique_id = str(uuid.uuid4())[:8]
        return f"odoo_{company_name}_{timestamp}_{unique_id}"

    def fetch_instance_api_key(self):
        """Fetch the instance-specific API key from Evolution API"""
        self.ensure_one()

        if not self.instance_name:
            _logger.warning("No instance name available to fetch API key")
            raise UserError(_('No instance name available to fetch API key'))

        try:
            url = f"{self.server_url.rstrip('/')}/instance/fetchInstances"
            headers = {
                'apikey': self._get_global_api_key()
            }

            _logger.info(f"Fetching instances from: {url}")
            _logger.info(f"Looking for instance: {self.instance_name}")
            _logger.info(f"Using global API key: {self._get_global_api_key()[:10]}...")

            # First try without parameters to get all instances
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()

            instances = response.json()
            _logger.info(f"API Response: {instances}")

            # Find our instance in the response
            # Based on actual API response structure
            found_instances = []
            for instance_data in instances:
                # The instance data is directly in the array, not nested under 'instance'
                instance_name = instance_data.get('name')

                # Only add non-None instance names to the list
                if instance_name:
                    found_instances.append(instance_name)

                if instance_name == self.instance_name:
                    # The API key field is called 'token', not 'apikey'
                    api_key = instance_data.get('token')
                    if api_key:
                        self.api_key = api_key
                        _logger.info(f"Successfully fetched instance API key (token) for {self.instance_name}: {api_key}")
                        return True
                    else:
                        _logger.warning(f"No token found in instance data for {self.instance_name}")
                        raise UserError(_('Instance found but no API token available. Instance may not be properly configured.'))

            # Create a safe list of instance names for display
            available_instances_str = ', '.join(found_instances) if found_instances else 'None'
            _logger.warning(f"Instance {self.instance_name} not found. Available instances: {found_instances}")
            raise UserError(_('Instance "%s" not found in Evolution API. Available instances: %s') % (self.instance_name, available_instances_str))

        except requests.exceptions.RequestException as e:
            error_msg = f"API request failed: {str(e)}"
            _logger.error(error_msg)
            raise UserError(_('Failed to connect to Evolution API: %s') % str(e))
        except Exception as e:
            error_msg = f"Failed to fetch instance API key: {str(e)}"
            _logger.error(error_msg)
            raise UserError(_('Unexpected error: %s') % str(e))

    def _get_global_api_key(self):
        """Get the global Evolution API key from configuration"""
        return self.global_api_key

    def create_evolution_instance(self):
        """Create a new instance in Evolution API"""
        self.ensure_one()

        if not self.server_url:
            raise UserError(_('Server URL is required to create an instance.'))

        # Get global API key
        global_api_key = self._get_global_api_key()
        if not global_api_key:
            raise UserError(_(
                "Global API Key is required to create instances. "
                "Please enter your Evolution API global authentication key."
            ))

        # Generate unique instance name if not provided
        if not self.instance_name:
            self.instance_name = self._generate_instance_name()

        url = f"{self.server_url.rstrip('/')}/instance/create"
        headers = {
            'Content-Type': 'application/json',
            'apikey': global_api_key
        }

        payload = {
            'instanceName': self.instance_name,
            'qrcode': True,
            'integration': 'WHATSAPP-BAILEYS',
            'rejectCall': True,
            'msgCall': 'Sorry, I cannot take calls at this time.',
            'groupsIgnore': False,
            'alwaysOnline': True,
            'readMessages': True,
            'readStatus': True,
            'syncFullHistory': False
        }

        try:
            self.instance_status = 'creating'

            response = requests.post(url, headers=headers, json=payload, timeout=30)
            response.raise_for_status()

            _logger.info(f"Evolution API response status: {response.status_code}")
            _logger.info(f"Evolution API response text: {response.text}")

            # Try to parse JSON response
            try:
                response_data = response.json()
                _logger.info(f"Parsed response data: {response_data}")
            except ValueError as json_error:
                _logger.error(f"Failed to parse JSON response: {json_error}")
                _logger.error(f"Raw response: {response.text}")
                raise UserError(_('Invalid JSON response from Evolution API: %s') % response.text)

            # Ensure response_data is a dictionary
            if not isinstance(response_data, dict):
                _logger.error(f"Response is not a dictionary: {type(response_data)} - {response_data}")
                raise UserError(_('Unexpected response format from Evolution API'))

            # Extract instance information safely based on actual API response
            instance_info = response_data.get('instance', {}) if isinstance(response_data, dict) else {}
            qr_info = response_data.get('qrcode', {}) if isinstance(response_data, dict) else {}
            hash_value = response_data.get('hash', '') if isinstance(response_data, dict) else ''

            # Set instance ID from the response
            if instance_info and isinstance(instance_info, dict):
                self.instance_id = instance_info.get('instanceId') or instance_info.get('instanceName') or self.instance_name
            else:
                self.instance_id = self.instance_name

            # The hash field contains a string, not an API key object
            # For now, we'll keep the global API key as the instance API key
            # Evolution API v2 doesn't seem to return instance-specific API keys
            _logger.info(f"Instance hash: {hash_value}")

            # Extract QR code information directly from create response
            # Based on actual Evolution API response structure
            if qr_info and isinstance(qr_info, dict):
                self.qr_code_data = qr_info.get('code', '')
                base64_data = qr_info.get('base64', '')
                self.pairing_code = qr_info.get('pairingCode', '')

                # Clean base64 data for Odoo (remove data:image/png;base64, prefix)
                # Evolution API returns: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
                # Odoo needs just: "iVBORw0KGgoAAAANSUhEUgAA..."
                if base64_data and base64_data.startswith('data:image/png;base64,'):
                    base64_data = base64_data.replace('data:image/png;base64,', '')

                self.qr_code_base64 = base64_data
                _logger.info(f"QR code extracted: code length={len(self.qr_code_data)}, base64 length={len(base64_data)}, pairing_code={self.pairing_code}")

            self.instance_status = 'waiting_qr'

            _logger.info(f"Successfully created Evolution API instance: {self.instance_name}")

            return response_data

        except requests.exceptions.RequestException as e:
            error_msg = str(e)
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_data = e.response.json()
                    error_msg = error_data.get('message', error_msg)
                except:
                    error_msg = e.response.text or error_msg

            self.instance_status = 'error'
            self.last_error_message = error_msg

            _logger.error(f"Failed to create Evolution API instance: {error_msg}")
            raise UserError(_('Failed to create WhatsApp instance: %s') % error_msg)

    def get_qr_code(self):
        """Get QR code for instance connection"""
        self.ensure_one()

        if not self.instance_name:
            raise UserError(_('No instance name configured.'))

        url = f"{self.server_url.rstrip('/')}/instance/connect/{self.instance_name}"

        # Use instance API key if available, otherwise use global API key
        api_key = self.api_key or self._get_global_api_key()
        if not api_key:
            raise UserError(_('No API key available for authentication.'))

        headers = {
            'apikey': api_key
        }

        try:
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()

            response_data = response.json()
            _logger.info(f"QR code response: {response_data}")

            # Extract QR code information - Evolution API returns it in qrcode object
            if 'qrcode' in response_data and isinstance(response_data['qrcode'], dict):
                qr_info = response_data['qrcode']
                self.qr_code_data = qr_info.get('code', '')
                base64_data = qr_info.get('base64', '')
                self.pairing_code = qr_info.get('pairingCode', '')
            else:
                # Fallback: Direct response format
                self.qr_code_data = response_data.get('code', '')
                base64_data = response_data.get('base64', '')
                self.pairing_code = response_data.get('pairingCode', '')

            # Clean base64 data for Odoo (remove data:image/png;base64, prefix)
            if base64_data and base64_data.startswith('data:image/png;base64,'):
                base64_data = base64_data.replace('data:image/png;base64,', '')

            self.qr_code_base64 = base64_data

            # Generate QR image if we have the code but no base64
            if self.qr_code_data and not self.qr_code_base64:
                self._generate_qr_image()

            self.instance_status = 'waiting_qr'

            _logger.info(f"Successfully retrieved QR code for instance: {self.instance_name}")

            return response_data

        except requests.exceptions.RequestException as e:
            error_msg = str(e)
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_data = e.response.json()
                    error_msg = error_data.get('message', error_msg)
                except:
                    error_msg = e.response.text or error_msg

            self.instance_status = 'error'
            self.last_error_message = error_msg

            _logger.error(f"Failed to get QR code: {error_msg}")
            raise UserError(_('Failed to get QR code: %s') % error_msg)

    def check_connection_status(self):
        """Check the connection status of the instance"""
        self.ensure_one()

        if not self.instance_name:
            return False

        url = f"{self.server_url.rstrip('/')}/instance/connectionState/{self.instance_name}"

        # Use instance API key if available, otherwise use global API key
        api_key = self.api_key or self._get_global_api_key()
        if not api_key:
            _logger.warning(f"No API key available for checking connection status of {self.instance_name}")
            return False

        headers = {
            'apikey': api_key
        }

        try:
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()

            response_data = response.json()
            connection_state = response_data.get('instance', {}).get('state', 'close')

            self.last_connection_check = fields.Datetime.now()

            # Update status based on connection state
            if connection_state == 'open':
                if self.instance_status != 'connected':
                    self.instance_status = 'connected'
                    self.connection_status = 'connected'
                    self.qr_code_data = False  # Clear QR code when connected
                    self.qr_code_base64 = False
                    self.pairing_code = False

                    _logger.info(f"Instance {self.instance_name} is now connected")
                return True
            elif connection_state == 'connecting':
                self.instance_status = 'connecting'
                return False
            else:
                if self.instance_status == 'connected':
                    self.instance_status = 'disconnected'
                    self.connection_status = 'error'
                    _logger.warning(f"Instance {self.instance_name} disconnected")
                return False

        except requests.exceptions.RequestException as e:
            error_msg = str(e)
            self.last_connection_check = fields.Datetime.now()
            self.last_error_message = error_msg

            if self.instance_status == 'connected':
                self.instance_status = 'error'
                self.connection_status = 'error'

            _logger.error(f"Failed to check connection status: {error_msg}")
            return False

    def _generate_qr_image(self):
        """Generate QR code image from QR data"""
        if not self.qr_code_data:
            return

        try:
            import qrcode
            import io
            import base64
            from PIL import Image

            # Generate QR code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(self.qr_code_data)
            qr.make(fit=True)

            # Create image
            img = qr.make_image(fill_color="black", back_color="white")

            # Convert to base64
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()

            self.qr_code_base64 = img_str

        except ImportError:
            _logger.warning("QR code generation requires 'qrcode' and 'Pillow' packages")
        except Exception as e:
            _logger.error(f"Failed to generate QR code image: {e}")

    def _start_reconnection_process(self):
        """Start the reconnection process"""
        self.ensure_one()

        try:
            # First try to get QR code for existing instance
            self.get_qr_code()

            return {
                'type': 'ir.actions.act_window',
                'name': _('Reconnect WhatsApp Instance'),
                'res_model': 'whatsapp.instance.wizard',
                'view_mode': 'form',
                'target': 'new',
                'context': {
                    'default_config_id': self.id,
                    'default_is_reconnection': True,
                }
            }

        except Exception as e:
            # If getting QR code fails, might need to create new instance
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Reconnection Failed'),
                    'message': _('Cannot reconnect existing instance. Please setup a new instance.'),
                    'type': 'warning',
                }
            }

    def delete_evolution_instance(self):
        """Delete the instance from Evolution API"""
        self.ensure_one()

        if not self.instance_name:
            return True

        url = f"{self.server_url.rstrip('/')}/instance/delete/{self.instance_name}"
        headers = {
            'apikey': self.api_key
        }

        try:
            response = requests.delete(url, headers=headers, timeout=30)
            response.raise_for_status()

            # Clear instance data
            self.instance_name = False
            self.instance_id = False
            self.instance_status = 'disconnected'
            self.qr_code_data = False
            self.qr_code_base64 = False
            self.pairing_code = False
            self.connection_status = 'not_tested'

            _logger.info(f"Successfully deleted Evolution API instance")

            return True

        except requests.exceptions.RequestException as e:
            error_msg = str(e)
            _logger.error(f"Failed to delete Evolution API instance: {error_msg}")
            # Don't raise error, just log it
            return False

    @api.model
    def _monitor_all_connections(self):
        """Scheduled action to monitor all active instance connections"""
        configs = self.search([
            ('active', '=', True),
            ('instance_name', '!=', False),
            ('instance_status', 'in', ['connected', 'connecting'])
        ])

        for config in configs:
            try:
                config.check_connection_status()

                # Auto-reconnect if enabled and disconnected
                if (config.auto_reconnect and
                    config.instance_status == 'disconnected' and
                    config.last_connection_check):

                    # Only try reconnection if last check was more than 5 minutes ago
                    time_diff = fields.Datetime.now() - config.last_connection_check
                    if time_diff.total_seconds() > 300:  # 5 minutes
                        try:
                            config.get_qr_code()
                            _logger.info(f"Auto-reconnection initiated for {config.instance_name}")
                        except Exception as e:
                            _logger.error(f"Auto-reconnection failed for {config.instance_name}: {e}")

            except Exception as e:
                _logger.error(f"Connection monitoring failed for config {config.id}: {e}")

    def action_view_messages(self):
        """View messages sent through this configuration"""
        self.ensure_one()

        return {
            'type': 'ir.actions.act_window',
            'name': _('WhatsApp Messages'),
            'res_model': 'whatsapp.message',
            'view_mode': 'tree,form',
            'domain': [('config_id', '=', self.id)],
            'context': {'default_config_id': self.id}
        }

    def action_refresh_status(self):
        """Manually refresh connection status"""
        self.ensure_one()

        try:
            is_connected = self.check_connection_status()

            if is_connected:
                message = _('Instance is connected and ready!')
                msg_type = 'success'
            else:
                message = _('Instance is not connected. Please check your setup.')
                msg_type = 'warning'

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Status Updated'),
                    'message': message,
                    'type': msg_type,
                }
            }

        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Failed to check status: %s') % str(e),
                    'type': 'danger',
                }
            }

    def action_fetch_api_key(self):
        """Manually fetch the instance API key"""
        self.ensure_one()

        try:
            self.fetch_instance_api_key()
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success!'),
                    'message': _('Instance API key has been fetched and updated successfully.'),
                    'type': 'success',
                }
            }
        except UserError:
            # Re-raise UserError as-is (it already has the proper message)
            raise
        except Exception as e:
            raise UserError(_('Unexpected error while fetching API key: %s') % str(e))

    def send_media_message(self, phone_number, media_url, caption=None, media_type='image'):
        """Send a WhatsApp media message using Evolution API"""
        self.ensure_one()

        if not self.active:
            raise UserError(_('This WhatsApp configuration is not active.'))

        if self.instance_status != 'connected':
            raise UserError(_('WhatsApp instance is not connected. Please setup the instance first.'))

        # Clean phone number
        clean_phone = ''.join(filter(str.isdigit, phone_number))
        clean_phone = clean_phone.replace('+', '')

        url = f"{self.server_url.rstrip('/')}/message/sendMedia/{self.instance_name}"
        headers = {
            'Content-Type': 'application/json',
            'apikey': self.api_key
        }

        payload = {
            'number': clean_phone,
            'mediatype': media_type,
            'media': media_url
        }

        if caption:
            payload['caption'] = caption

        try:
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            response.raise_for_status()

            response_data = response.json()

            # Log the message
            self.env['whatsapp.message'].create({
                'config_id': self.id,
                'phone_number': phone_number,
                'message_text': caption or f'Media: {media_url}',
                'status': 'sent',
                'response_data': json.dumps(response_data),
                'sent_date': fields.Datetime.now(),
            })

            return response_data

        except requests.exceptions.RequestException as e:
            error_msg = str(e)
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_data = e.response.json()
                    error_msg = error_data.get('message', error_msg)
                except:
                    error_msg = e.response.text or error_msg

            # Log the failed message
            self.env['whatsapp.message'].create({
                'config_id': self.id,
                'phone_number': phone_number,
                'message_text': caption or f'Media: {media_url}',
                'status': 'failed',
                'error_message': error_msg,
                'sent_date': fields.Datetime.now(),
            })

            _logger.error(f"Failed to send WhatsApp media message: {error_msg}")
            raise UserError(_('Failed to send WhatsApp media message: %s') % error_msg)

    def get_instance_info(self):
        """Get detailed instance information from Evolution API"""
        self.ensure_one()

        if not self.instance_name:
            return {}

        url = f"{self.server_url.rstrip('/')}/instance/fetchInstances"
        headers = {
            'apikey': self.api_key
        }

        try:
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()

            response_data = response.json()

            # Find our instance in the list
            for instance in response_data:
                if instance.get('instance', {}).get('instanceName') == self.instance_name:
                    return instance

            return {}

        except requests.exceptions.RequestException as e:
            _logger.error(f"Failed to get instance info: {e}")
            return {}

    @api.model
    def _validate_evolution_api_config(self, server_url, api_key):
        """Validate Evolution API configuration"""
        try:
            url = f"{server_url.rstrip('/')}"
            headers = {
                'Content-Type': 'application/json',
                'apikey': api_key
            }

            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200:
                return True, _('Connection successful')
            else:
                return False, f"HTTP {response.status_code}: {response.text}"

        except Exception as e:
            return False, str(e)

    def _ensure_instance_connected(self):
        """Ensure instance is connected before operations"""
        self.ensure_one()

        if not self.instance_name:
            raise UserError(_('No WhatsApp instance configured. Please setup an instance first.'))

        if self.instance_status != 'connected':
            # Try to check current status
            is_connected = self.check_connection_status()
            if not is_connected:
                raise UserError(_('WhatsApp instance is not connected. Please reconnect your instance.'))

    def action_force_disconnect(self):
        """Force disconnect the instance"""
        self.ensure_one()

        if not self.instance_name:
            return

        url = f"{self.server_url.rstrip('/')}/instance/logout/{self.instance_name}"
        headers = {
            'apikey': self.api_key
        }

        try:
            response = requests.delete(url, headers=headers, timeout=30)
            response.raise_for_status()

            self.instance_status = 'disconnected'
            self.connection_status = 'not_tested'
            self.qr_code_data = False
            self.qr_code_base64 = False
            self.pairing_code = False

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Disconnected'),
                    'message': _('WhatsApp instance has been disconnected.'),
                    'type': 'success',
                }
            }

        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Failed to disconnect: %s') % str(e),
                    'type': 'danger',
                }
            }
