# WhatsApp Evolution API Integration for Odoo 18

This module integrates Odoo 18 with Evolution API to send WhatsApp messages directly from your Odoo instance.

## Features

- 🚀 **Easy Configuration**: Simple setup interface for Evolution API connection
- 📱 **Send Messages**: Send WhatsApp messages with a user-friendly interface
- 📊 **Message Logging**: Track all sent messages with status and response data
- 🔧 **Test Functionality**: Built-in test message feature to verify configuration
- 🏢 **Multi-Company**: Support for multiple companies with separate configurations
- 🔐 **Security**: Role-based access control with user and manager groups
- 📈 **Status Tracking**: Monitor message delivery status and error handling

## Requirements

### Evolution API Server
- Evolution API v2 server running and accessible
- Valid API key configured in Evolution API
- WhatsApp instance connected and active

### Odoo Dependencies
- Odoo 18.0
- Base modules (included in standard Odoo installation)

## Installation

1. Copy the `whatsapp_evolution` folder to your Odoo `custom_addons` directory
2. Update the app list in Odoo
3. Install the "WhatsApp Evolution API Integration" module

## Configuration

### Step 1: Evolution API Setup
1. Ensure your Evolution API server is running
2. Create a WhatsApp instance in Evolution API
3. Note down your API key and instance name

### Step 2: Odoo Configuration
1. Go to **WhatsApp > Configuration > WhatsApp Settings**
2. Create a new configuration or edit the default one
3. Fill in the required fields:
   - **Server URL**: Your Evolution API server URL (e.g., `https://your-server.com`)
   - **API Key**: Your Evolution API authentication key
   - **Instance Name**: Your WhatsApp instance name
4. Click **Test Connection** to verify the setup
5. Mark as **Default Configuration** if this is your primary setup

### Step 3: Test the Integration
1. Click **Send Test Message** in the configuration
2. Enter a phone number (with country code, e.g., +**********)
3. Send the test message to verify everything works

## Usage

### Sending Messages

#### Via Menu
1. Go to **WhatsApp > Messages > Send Message**
2. Select your configuration
3. Enter the phone number (with country code)
4. Type your message
5. Click **Send Message**

#### Via Configuration
1. Go to **WhatsApp > Configuration > WhatsApp Settings**
2. Open a configuration
3. Click **Send Test Message**

### Viewing Message Log
1. Go to **WhatsApp > Messages > Message Log**
2. View all sent messages with their status
3. Filter by status, date, or configuration
4. Resend failed messages if needed

## API Integration

### Programmatic Usage

```python
# Get default configuration
config = self.env['whatsapp.config'].get_default_config()

# Send a message
if config:
    try:
        response = config.send_message(
            phone_number='+**********',
            message_text='Hello from Odoo!',
            delay=1000  # Optional delay in milliseconds
        )
        print(f"Message sent successfully: {response}")
    except Exception as e:
        print(f"Failed to send message: {e}")
```

### Evolution API Endpoints Used

- **POST** `/message/sendText/{instance}` - Send text messages
- **GET** `/instance/connectionState/{instance}` - Check connection status
- **GET** `/` - Basic API health check

## Security

### User Groups

- **WhatsApp User**: Can send messages and view message logs
- **WhatsApp Manager**: Can configure settings and manage all operations

### Access Control

- Users can only access configurations and messages from their company
- Managers have full access to configuration and management features
- System administrators have access via Technical menu

## Troubleshooting

### Common Issues

1. **Connection Test Fails**
   - Verify Evolution API server is running and accessible
   - Check API key is correct
   - Ensure firewall allows connections

2. **Message Sending Fails**
   - Verify WhatsApp instance is connected in Evolution API
   - Check phone number format (must include country code)
   - Review error message in message log

3. **Phone Number Format**
   - Always include country code (e.g., +1 for US)
   - Remove spaces and special characters
   - Example: +********** (not ******-567-8900)

### Debug Mode

Enable developer mode in Odoo to access:
- **Settings > Technical > WhatsApp Evolution**
- Detailed error logs in server logs
- Raw API response data

## Evolution API Documentation

For detailed Evolution API documentation, visit:
- [Evolution API Documentation](https://doc.evolution-api.com)
- [Send Text Message Endpoint](https://doc.evolution-api.com/v2/api-reference/message-controller/send-text)

## Support

For issues related to:
- **This Odoo module**: Check the module logs and configuration
- **Evolution API**: Refer to Evolution API documentation and community
- **WhatsApp connectivity**: Ensure your WhatsApp instance is properly connected

## License

This module is licensed under LGPL-3.

## Contributing

Contributions are welcome! Please ensure:
- Code follows Odoo development guidelines
- Proper testing of new features
- Documentation updates for new functionality

---

**Note**: This module requires a working Evolution API server. Make sure your Evolution API instance is properly configured and your WhatsApp account is connected before using this integration.
