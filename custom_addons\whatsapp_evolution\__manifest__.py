# -*- coding: utf-8 -*-
{
    'name': 'WhatsApp Evolution API Integration',
    'version': '********.0',
    'category': 'Communication',
    'summary': 'Send WhatsApp messages using Evolution API',
    'description': """
WhatsApp Evolution API Integration
==================================

This module integrates Odoo with Evolution API to send WhatsApp messages.

Features:
---------
* Configure Evolution API server settings
* Send test WhatsApp messages
* Integration with Evolution API v2
* Support for multiple instances
* Message logging and status tracking
* Easy configuration interface

Configuration:
--------------
1. Go to Settings > Technical > WhatsApp Evolution > Configuration
2. Configure your Evolution API server URL, API key, and instance name
3. Test the connection
4. Start sending WhatsApp messages

Requirements:
-------------
* Evolution API server running and accessible
* Valid API key and instance configured in Evolution API
* WhatsApp instance connected and active

Evolution API Documentation:
----------------------------
https://doc.evolution-api.com/v2/api-reference/message-controller/send-text
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'depends': [
        'base',
        'base_setup',
        'stock',
        'product',
        'account',
    ],
    'data': [
        'security/whatsapp_evolution_security.xml',
        'security/ir.model.access.csv',
        'data/whatsapp_config_data.xml',
        'data/whatsapp_config_default_data.xml',
        'views/whatsapp_config_views.xml',
        'views/whatsapp_message_views.xml',
        'views/whatsapp_send_message_wizard_views.xml',
        'views/whatsapp_instance_wizard_views.xml',
        'views/whatsapp_business_template_views.xml',
        'views/menu_views.xml',
        'data/whatsapp_business_template_data.xml',
        'data/whatsapp_cron_data.xml',
    ],
    'demo': [
        'demo/whatsapp_config_demo.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': True,
    'license': 'LGPL-3',
    # Simple data file approach - no hooks needed
}
