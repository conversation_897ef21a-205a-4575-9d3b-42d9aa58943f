<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Security Groups -->
        <record id="group_daily_webhook_user" model="res.groups">
            <field name="name">Daily Webhook User</field>
            <field name="category_id" ref="base.module_category_sales"/>
            <field name="comment">Can view daily sales reports and webhook status</field>
        </record>
        
        <record id="group_daily_webhook_manager" model="res.groups">
            <field name="name">Daily Webhook Manager</field>
            <field name="category_id" ref="base.module_category_sales"/>
            <field name="implied_ids" eval="[(4, ref('group_daily_webhook_user'))]"/>
            <field name="comment">Can configure webhooks and manage daily sales reports</field>
        </record>
        
        <!-- Record Rules -->
        <record id="daily_sales_report_company_rule" model="ir.rule">
            <field name="name">Daily Sales Report: Company Rule</field>
            <field name="model_id" ref="model_daily_sales_report"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        </record>
        
        <!-- Give access to accounting users by default -->
        <record id="base.group_user" model="res.groups">
            <field name="implied_ids" eval="[(4, ref('group_daily_webhook_user'))]"/>
        </record>
        
        <record id="account.group_account_manager" model="res.groups">
            <field name="implied_ids" eval="[(4, ref('group_daily_webhook_manager'))]"/>
        </record>
        
    </data>
</odoo>
