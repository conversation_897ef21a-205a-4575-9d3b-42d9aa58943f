<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- WhatsApp Evolution Category -->
        <record id="module_category_whatsapp_evolution" model="ir.module.category">
            <field name="name">WhatsApp Evolution</field>
            <field name="description">WhatsApp Evolution API Integration</field>
            <field name="sequence">20</field>
        </record>

        <!-- WhatsApp Evolution User Group -->
        <record id="group_whatsapp_user" model="res.groups">
            <field name="name">WhatsApp User</field>
            <field name="category_id" ref="module_category_whatsapp_evolution"/>
            <field name="comment">Can send WhatsApp messages and view message logs</field>
        </record>

        <!-- WhatsApp Evolution Manager Group -->
        <record id="group_whatsapp_manager" model="res.groups">
            <field name="name">WhatsApp Manager</field>
            <field name="category_id" ref="module_category_whatsapp_evolution"/>
            <field name="implied_ids" eval="[(4, ref('group_whatsapp_user'))]"/>
            <field name="comment">Can configure WhatsApp settings and manage all WhatsApp operations</field>
        </record>

        <!-- Record Rules -->
        
        <!-- WhatsApp Config - Users can only see their company's configs -->
        <record id="whatsapp_config_rule_user" model="ir.rule">
            <field name="name">WhatsApp Config: User Access</field>
            <field name="model_id" ref="model_whatsapp_config"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_whatsapp_user'))]"/>
        </record>

        <!-- WhatsApp Message - Users can only see their company's messages -->
        <record id="whatsapp_message_rule_user" model="ir.rule">
            <field name="name">WhatsApp Message: User Access</field>
            <field name="model_id" ref="model_whatsapp_message"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_whatsapp_user'))]"/>
        </record>

    </data>
</odoo>
