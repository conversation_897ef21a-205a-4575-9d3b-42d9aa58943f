# WhatsApp Normal User Guide

## Overview
The WhatsApp Evolution module now includes a special "Normal User" access level designed for clients who need to reconnect their WhatsApp when disconnected, without access to technical settings.

## User Groups

### 1. WhatsApp Manager (Technical Team)
- Full access to all configurations
- Can modify server URLs, API keys
- Can create/delete instances
- Access to all technical settings

### 2. WhatsApp User (Power Users)
- Can send messages and view logs
- Limited configuration access
- Can manage business templates

### 3. WhatsApp Normal User (Clients) - NEW
- **ONLY** access to reconnection functionality
- Cannot see technical settings
- Cannot modify configurations
- Simple, clean interface

## How to Set Up Normal Users

### Step 1: Assign User Group
1. Go to **Settings > Users & Companies > Users**
2. Select the client user
3. In the **Access Rights** tab, find **WhatsApp Evolution**
4. Select **WhatsApp Normal User**
5. Save the user

### Step 2: Client Access
Once assigned, the client will see:
- New menu: **My WhatsApp > WhatsApp Connection**
- Simple interface showing connection status
- Reconnect button when needed

## What Normal Users See

### Connected State
```
✅ WhatsApp Connected
Your WhatsApp is connected and ready to send messages.
Last checked: 2 minutes ago
[Refresh Status]
```

### Disconnected State
```
❌ WhatsApp Disconnected
Your WhatsApp connection has been lost. Click the "Reconnect WhatsApp" button to restore the connection.
[Reconnect WhatsApp]
```

### Reconnecting State
```
🔄 Connecting WhatsApp...
Please scan this QR code with your WhatsApp:
[QR CODE IMAGE]
Alternative: Use pairing code: 12345678
```

## What Normal Users CANNOT See
- Server URLs
- API keys
- Instance names/IDs
- Technical configuration fields
- Advanced settings
- Create/delete instances
- Message logs (optional)

## Benefits
- **Security**: Clients can't access sensitive settings
- **Simplicity**: Clean, focused interface for clients
- **Control**: Technical team maintains full control
- **User-Friendly**: Non-technical users can easily reconnect
- **Scalable**: Easy to add more clients with same access level

## Menu Structure for Normal Users
```
My WhatsApp
└── WhatsApp Connection
    ├── Connection Status
    └── Reconnect (when needed)
```

## Technical Notes
- Normal users can only see their company's WhatsApp instances
- All technical fields are hidden using security groups
- Reconnection uses the same backend methods but simplified interface
- QR codes are displayed automatically when needed
- Status updates in real-time
