<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Daily Sales & Payment Webhook</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #875A7B; color: white; padding: 20px; border-radius: 5px; }
        .content { margin: 20px 0; }
        .feature { margin: 10px 0; padding: 10px; background: #f9f9f9; border-left: 4px solid #875A7B; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Daily Sales & Payment Webhook</h1>
        <p>Automated daily reporting to n8n webhooks for WhatsApp integration</p>
    </div>
    
    <div class="content">
        <h2>Features</h2>
        
        <div class="feature">
            <h3>📊 Automated Daily Reports</h3>
            <p>Automatically collects and sends daily sales and payment data at configurable times.</p>
        </div>
        
        <div class="feature">
            <h3>🔗 n8n Webhook Integration</h3>
            <p>Seamlessly integrates with n8n automation workflows for WhatsApp notifications.</p>
        </div>
        
        <div class="feature">
            <h3>🏢 Multi-Company Support</h3>
            <p>Each company can have its own webhook URL and configuration settings.</p>
        </div>
        
        <div class="feature">
            <h3>🔄 Retry Mechanism</h3>
            <p>Automatic retry for failed webhook calls with configurable retry count and delay.</p>
        </div>
        
        <div class="feature">
            <h3>📈 Comprehensive Reporting</h3>
            <p>Track webhook status, view historical data, and analyze trends with built-in reports.</p>
        </div>
        
        <h2>Configuration</h2>
        <ol>
            <li>Go to Settings → Companies → Company Settings</li>
            <li>Navigate to the "Daily Webhook Settings" tab</li>
            <li>Enable the webhook and configure your n8n webhook URL</li>
            <li>Set your preferred reporting time and retry settings</li>
            <li>Test the webhook connection</li>
        </ol>
        
        <h2>Data Format</h2>
        <p>The webhook sends JSON data including:</p>
        <ul>
            <li>Company name and database identifier</li>
            <li>Total invoiced sales for the day</li>
            <li>Total customer payments received</li>
            <li>Transaction counts and currency information</li>
            <li>Timestamp and metadata</li>
        </ul>
    </div>
</body>
</html>
