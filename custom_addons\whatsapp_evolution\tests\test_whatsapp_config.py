# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError
from unittest.mock import patch, MagicMock


class TestWhatsAppConfig(TransactionCase):

    def setUp(self):
        super().setUp()
        self.WhatsAppConfig = self.env['whatsapp.config']
        
    def test_create_config(self):
        """Test creating a WhatsApp configuration"""
        config = self.WhatsAppConfig.create({
            'name': 'Test Config',
            'server_url': 'https://test-server.com',
            'api_key': 'test-api-key',
            'instance_name': 'test_instance',
        })
        
        self.assertEqual(config.name, 'Test Config')
        self.assertEqual(config.server_url, 'https://test-server.com')
        self.assertEqual(config.api_key, 'test-api-key')
        self.assertEqual(config.instance_name, 'test_instance')
        self.assertTrue(config.active)
        self.assertEqual(config.connection_status, 'not_tested')

    def test_default_config_constraint(self):
        """Test that only one default configuration is allowed per company"""
        # Create first default config
        config1 = self.WhatsAppConfig.create({
            'name': 'Default Config 1',
            'server_url': 'https://test1.com',
            'api_key': 'key1',
            'instance_name': 'instance1',
            'is_default': True,
        })
        
        # Try to create second default config - should raise ValidationError
        with self.assertRaises(ValidationError):
            self.WhatsAppConfig.create({
                'name': 'Default Config 2',
                'server_url': 'https://test2.com',
                'api_key': 'key2',
                'instance_name': 'instance2',
                'is_default': True,
            })

    def test_get_default_config(self):
        """Test getting the default configuration"""
        # Create a default config
        config = self.WhatsAppConfig.create({
            'name': 'Default Config',
            'server_url': 'https://default.com',
            'api_key': 'default-key',
            'instance_name': 'default_instance',
            'is_default': True,
        })
        
        default_config = self.WhatsAppConfig.get_default_config()
        self.assertEqual(default_config.id, config.id)

    def test_phone_number_cleaning(self):
        """Test phone number cleaning in send_message method"""
        config = self.WhatsAppConfig.create({
            'name': 'Test Config',
            'server_url': 'https://test.com',
            'api_key': 'test-key',
            'instance_name': 'test',
        })
        
        # Mock the requests.post to avoid actual API calls
        with patch('requests.post') as mock_post:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                'key': {'id': 'test123', 'remoteJid': '<EMAIL>'},
                'status': 'PENDING'
            }
            mock_post.return_value = mock_response
            
            # Test various phone number formats
            test_numbers = [
                '+1234567890',
                '1234567890',
                '******-567-8900',
                '(*************'
            ]
            
            for number in test_numbers:
                try:
                    config.send_message(number, 'Test message')
                    # If we get here, the method handled the number format
                    self.assertTrue(True)
                except Exception as e:
                    # Should not raise exceptions for valid numbers
                    self.fail(f"send_message failed for number {number}: {e}")

    @patch('requests.get')
    def test_connection_test_success(self, mock_get):
        """Test successful connection test"""
        config = self.WhatsAppConfig.create({
            'name': 'Test Config',
            'server_url': 'https://test.com',
            'api_key': 'test-key',
            'instance_name': 'test',
        })
        
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        result = config.test_connection()
        
        self.assertEqual(config.connection_status, 'connected')
        self.assertIsNotNone(config.last_test_date)
        self.assertFalse(config.last_error_message)
        self.assertEqual(result['params']['type'], 'success')

    @patch('requests.get')
    def test_connection_test_failure(self, mock_get):
        """Test failed connection test"""
        config = self.WhatsAppConfig.create({
            'name': 'Test Config',
            'server_url': 'https://test.com',
            'api_key': 'test-key',
            'instance_name': 'test',
        })
        
        # Mock failed response
        mock_get.side_effect = Exception('Connection failed')
        
        result = config.test_connection()
        
        self.assertEqual(config.connection_status, 'error')
        self.assertIsNotNone(config.last_test_date)
        self.assertIn('Connection failed', config.last_error_message)
        self.assertEqual(result['params']['type'], 'danger')
