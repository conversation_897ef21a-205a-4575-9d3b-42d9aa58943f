<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Demo WhatsApp Configuration -->
        <record id="demo_whatsapp_config" model="whatsapp.config">
            <field name="name">Demo WhatsApp Configuration</field>
            <field name="server_url">https://demo-evolution-api.com</field>
            <field name="api_key">demo-api-key-12345</field>
            <field name="instance_name">demo_instance</field>
            <field name="active" eval="True"/>
            <field name="is_default" eval="True"/>
        </record>

        <!-- Demo WhatsApp Messages -->
        <record id="demo_whatsapp_message_1" model="whatsapp.message">
            <field name="config_id" ref="demo_whatsapp_config"/>
            <field name="phone_number">+1234567890</field>
            <field name="message_text">Hello! This is a demo WhatsApp message from Odoo.</field>
            <field name="status">sent</field>
            <field name="sent_date" eval="(datetime.now() - timedelta(hours=2))"/>
            <field name="response_data">{"key": {"remoteJid": "<EMAIL>", "fromMe": true, "id": "DEMO123"}, "message": {"extendedTextMessage": {"text": "Hello! This is a demo WhatsApp message from Odoo."}}, "messageTimestamp": "1234567890", "status": "PENDING"}</field>
        </record>

        <record id="demo_whatsapp_message_2" model="whatsapp.message">
            <field name="config_id" ref="demo_whatsapp_config"/>
            <field name="phone_number">+9876543210</field>
            <field name="message_text">Welcome to our service! Thank you for choosing us.</field>
            <field name="status">sent</field>
            <field name="sent_date" eval="(datetime.now() - timedelta(hours=1))"/>
            <field name="response_data">{"key": {"remoteJid": "<EMAIL>", "fromMe": true, "id": "DEMO456"}, "message": {"extendedTextMessage": {"text": "Welcome to our service! Thank you for choosing us."}}, "messageTimestamp": "1234567891", "status": "PENDING"}</field>
        </record>

        <record id="demo_whatsapp_message_3" model="whatsapp.message">
            <field name="config_id" ref="demo_whatsapp_config"/>
            <field name="phone_number">+5555555555</field>
            <field name="message_text">This message failed to send due to network error.</field>
            <field name="status">failed</field>
            <field name="sent_date" eval="(datetime.now() - timedelta(minutes=30))"/>
            <field name="error_message">Network timeout: Unable to connect to WhatsApp servers</field>
        </record>

    </data>
</odoo>
